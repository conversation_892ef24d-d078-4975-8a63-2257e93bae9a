// Noise.js - Génération de terrain avec Perlin
export class Noise {
    constructor(seed) {
        this.seed = seed || Math.floor(Math.random() * 65536);
        this.gradients = {};
        this.memory = {};
        
        // Précalculer quelques valeurs pour améliorer les performances
        this.precalculate();
    }
    
    // Précalcul de valeurs aléatoires cohérentes
    precalculate() {
        // Créer une table de permutation pour un accès plus rapide
        this.perm = new Uint8Array(512);
        let seed = this.seed;
        
        for (let i = 0; i < 256; i++) {
            this.perm[i] = i;
        }
        
        // Mélanger la table de permutation
        for (let i = 255; i > 0; i--) {
            const j = Math.floor((seed = (seed * 16807) % 2147483647) / 2147483647 * (i + 1));
            [this.perm[i], this.perm[j]] = [this.perm[j], this.perm[i]];
            this.perm[i + 256] = this.perm[i];
        }
    }
    
    // Fonction de hachage pour générer des valeurs pseudo-aléatoires cohérentes
    hash(x, y, z = 0) {
        const key = `${x},${y},${z}`;
        if (this.memory[key] !== undefined) return this.memory[key];
        
        const value = ((Math.sin(x * 12.9898 + y * 78.233 + z * 37.719 + this.seed) * 43758.5453) % 1 + 1) % 1;
        this.memory[key] = value;
        return value;
    }
    
    // Générer un vecteur de gradient à partir d'un point de la grille
    getGradient(ix, iy, iz = 0) {
        const key = `${ix},${iy},${iz}`;
        if (!this.gradients[key]) {
            // Utiliser les coordonnées pour générer un angle pseudo-aléatoire cohérent
            const theta = this.hash(ix, iy, iz) * Math.PI * 2;
            const phi = this.hash(ix + 1, iy - 1, iz) * Math.PI * 2;
            
            // Créer un vecteur 3D sur la sphère unitaire
            this.gradients[key] = {
                x: Math.cos(theta) * Math.cos(phi),
                y: Math.sin(phi),
                z: Math.sin(theta) * Math.cos(phi)
            };
        }
        return this.gradients[key];
    }
    
    // Produit scalaire entre le gradient et le vecteur distance
    dotGridGradient(ix, iy, x, y, iz = 0, z = 0) {
        const gradient = this.getGradient(ix, iy, iz);
        const dx = x - ix;
        const dy = y - iy;
        const dz = z - iz;
        return dx * gradient.x + dy * gradient.y + dz * gradient.z;
    }
    
    // Fonction d'interpolation lissée (fonction de Perlin)
    smootherstep(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    // Interpolation linéaire
    lerp(a, b, t) {
        return a + t * (b - a);
    }
    
    // Bruit de Perlin 2D amélioré
    perlin(x, y, z = 0) {
        // Optimisation: utiliser un cache pour les valeurs fréquemment demandées
        const cacheKey = `${x.toFixed(4)},${y.toFixed(4)},${z.toFixed(4)}`;
        if (this.memory[cacheKey] !== undefined) return this.memory[cacheKey];
        
        const x0 = Math.floor(x);
        const y0 = Math.floor(y);
        const z0 = Math.floor(z);
        const x1 = x0 + 1;
        const y1 = y0 + 1;
        const z1 = z0 + 1;
        
        // Calcul de la fonction de lissage
        const sx = this.smootherstep(x - x0);
        const sy = this.smootherstep(y - y0);
        const sz = this.smootherstep(z - z0);
        
        // Interpolation sur l'axe x puis y pour la première tranche z
        const n0 = this.dotGridGradient(x0, y0, x, y, z0, z);
        const n1 = this.dotGridGradient(x1, y0, x, y, z0, z);
        const ix0 = this.lerp(n0, n1, sx);
        
        const n2 = this.dotGridGradient(x0, y1, x, y, z0, z);
        const n3 = this.dotGridGradient(x1, y1, x, y, z0, z);
        const ix1 = this.lerp(n2, n3, sx);
        
        const iz0 = this.lerp(ix0, ix1, sy);
        
        // Si on est en 2D, retourner directement le résultat
        if (z === 0 && z0 === 0 && z1 === 0) {
            // Normaliser entre -1 et 1
            const result = iz0;
            this.memory[cacheKey] = result;
            return result;
        }
        
        // Sinon, continuer avec l'interpolation 3D
        // Interpolation sur l'axe x puis y pour la deuxième tranche z
        const n4 = this.dotGridGradient(x0, y0, x, y, z1, z);
        const n5 = this.dotGridGradient(x1, y0, x, y, z1, z);
        const ix2 = this.lerp(n4, n5, sx);
        
        const n6 = this.dotGridGradient(x0, y1, x, y, z1, z);
        const n7 = this.dotGridGradient(x1, y1, x, y, z1, z);
        const ix3 = this.lerp(n6, n7, sx);
        
        const iz1 = this.lerp(ix2, ix3, sy);
        
        // Interpolation finale sur l'axe z
        const result = this.lerp(iz0, iz1, sz);
        this.memory[cacheKey] = result;
        return result;
    }
    
    // Fonction multi-octave (bruit de Perlin fractal) pour plus de détails à différentes échelles
    fractal(x, y, z = 0, octaves = 6, persistence = 0.5, lacunarity = 2.0) {
        let total = 0;
        let frequency = 1;
        let amplitude = 1;
        let maxValue = 0;  // Utilisé pour normaliser le résultat final
        
        // Additionner plusieurs octaves de bruit
        for (let i = 0; i < octaves; i++) {
            total += this.perlin(x * frequency, y * frequency, z * frequency) * amplitude;
            
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        // Normaliser le résultat entre -1 et 1
        return total / maxValue;
    }
    
    // Compatibilité avec l'ancienne API statique
    static perlin(x, y, z) {
        // Instance singleton
        if (!this._instance) {
            this._instance = new Noise(42); // seed fixe pour cohérence
        }
        return this._instance.perlin(x, y, z);
    }
}