La demande de verrouillage du pointeur a été refusée, car le document n’est pas sélectionné. Controls.js:38:24
📄 Logs sauvegardés: minecraft-js-logs-session-2025-07-22T16-22-30-840Z-yfpvld.html (1000 entrées) Logger.js:193:17
GET
http://localhost:8000/
[HTTP/1 304 Not Modified 1ms]

GET
http://localhost:8000/css/style.css
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/css/mining-ui.css
[HTTP/1 200 OK 0ms]

GET
https://unpkg.com/three@0.160.0/build/three.min.js
[HTTP/2 200  0ms]

Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
Propriété « -moz-user-drag » inconnue.  Déclaration abandonnée. mining-ui.css:226:20
Scripts "build/three.js" and "build/three.min.js" are deprecated with r150+, and will be removed with r160. Please use ES Modules or alternatives: https://threejs.org/docs/index.html#manual/en/introduction/Installation three.min.js:1:9
🔄 CACHE BUSTER ACTIVÉ - Timestamp: 1753202598819 localhost:8000:171:17
🎨 CSS chargé avec cache-buster: css/style.css?v=1753202598819 localhost:8000:187:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/version.js?v=1753202598819 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SmartLogger.js?v=1753202598819 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SimplexNoise.js?v=1753202598819 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/main.js?v=1753202598819 localhost:8000:179:21
GET
http://localhost:8000/css/style.css?v=1753202598819
[HTTP/1 200 OK 1ms]

GET
http://localhost:8000/js/version.js?v=1753202598819
[HTTP/1 200 OK 2ms]

GET
http://localhost:8000/js/utils/SimplexNoise.js?v=1753202598819
[HTTP/1 200 OK 3ms]

GET
http://localhost:8000/js/utils/SmartLogger.js?v=1753202598819
[HTTP/1 200 OK 5ms]

GET
http://localhost:8000/js/main.js?v=1753202598819
[HTTP/1 200 OK 2ms]

Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
🚀 ===== MINECRAFT JS - v1753202598883 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-22T16:43:18.883Z version.js:26:17
🔄 Cache Buster: 1753202598883 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753202598883 version.js:30:21
   ✅ WORLD: World-v1753202598883 version.js:30:21
   ✅ CHUNK: Chunk-v1753202598883 version.js:30:21
   ✅ CONTROLS: Controls-v1753202598883 version.js:30:21
   ✅ MAIN: Main-v1753202598883 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies version.js:45:17
GET
http://localhost:8000/favicon.ico
[HTTP/1 404 File not found 0ms]

GET
http://localhost:8000/js/version.js
[HTTP/1 200 OK 0ms]

🚀 ===== MINECRAFT JS - v1753202598917 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-22T16:43:18.917Z version.js:26:17
🔄 Cache Buster: 1753202598917 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753202598917 version.js:30:21
   ✅ WORLD: World-v1753202598917 version.js:30:21
   ✅ CHUNK: Chunk-v1753202598917 version.js:30:21
   ✅ CONTROLS: Controls-v1753202598917 version.js:30:21
   ✅ MAIN: Main-v1753202598917 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies 2 version.js:45:17
📦 Import avec cache-buster: ./utils/Logger.js?v=1753202598917 main.js:10:13
GET
http://localhost:8000/js/utils/Logger.js?v=1753202598917
[HTTP/1 200 OK 2ms]

[SYSTEM] Logger initialized 
Object { sessionId: "session-2025-07-22T16-43-18-931Z-e295ic", timestamp: "2025-07-22T16:43:18.931Z", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", url: "http://localhost:8000/" }
Logger.js:102:21
🔍 Logger initialisé - Session: session-2025-07-22T16-43-18-931Z-e295ic Logger.js:19:17
📦 Import avec cache-buster: ./utils/TextureGenerator.js?v=1753202598917 main.js:10:13
GET
http://localhost:8000/js/utils/TextureGenerator.js?v=1753202598917
[HTTP/1 200 OK 2ms]

📦 Import avec cache-buster: ./world/World.js?v=1753202598917 main.js:10:13
GET
http://localhost:8000/js/world/World.js?v=1753202598917
[HTTP/1 200 OK 2ms]

GET
http://localhost:8000/js/world/WorldGenerator.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/world/Chunk.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/utils/WorkerManager.js
[HTTP/1 200 OK 0ms]

📦 Import avec cache-buster: ./player/Player.js?v=1753202598917 main.js:10:13
GET
http://localhost:8000/js/player/Player.js?v=1753202598917
[HTTP/1 200 OK 2ms]

📦 Import avec cache-buster: ./player/Controls.js?v=1753202598917 main.js:10:13
GET
http://localhost:8000/js/player/Controls.js?v=1753202598917
[HTTP/1 200 OK 2ms]

📦 Import avec cache-buster: ./ui/OptionsManager.js?v=1753202598917 main.js:10:13
GET
http://localhost:8000/js/ui/OptionsManager.js?v=1753202598917
[HTTP/1 200 OK 2ms]

[INFO] Tous les modules importés avec succès 
Object { version: "v1753202598917", cacheBuster: 1753202598917 }
Logger.js:102:21
[INFO] Three.js chargé 
Object { revision: "160" }
Logger.js:102:21
[INFO] Modules de génération de terrain chargés 
Object {  }
Logger.js:102:21
[INFO] Renderer créé 
Object { size: {…}, pixelRatio: 1, canvas: "game" }
Logger.js:102:21
[INFO] Création du générateur de textures... 
Object {  }
Logger.js:102:21
[INFO] TextureGenerator initialisé 
Object { blockTypes: 15, textureSize: 16 }
Logger.js:102:21
[INFO] Générateur de textures créé 
Object {  }
Logger.js:102:21
[INFO] Création du monde... 
Object {  }
Logger.js:102:21
[INFO] Système de monde chargé 
Object { version: "World-v1753202599129", features: {…} }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 1179835 }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 1179837 }
Logger.js:102:21
[INFO] Monde créé 
Object { chunks: 0 }
Logger.js:102:21
[INFO] Création du joueur... 
Object {  }
Logger.js:102:21
[INFO] Player initialisé 
Object { version: "Player-v1753202599138", features: {…} }
Logger.js:102:21
[INFO] Caméra initialisée 
Object { position: {…}, rotation: {…} }
Logger.js:102:21
[INFO] Joueur créé 
Object { position: {…}, flyMode: false }
Logger.js:102:21
[INFO] Création des contrôles... 
Object {  }
Logger.js:102:21
[INFO] Contrôles initialisés 
Object { sensitivity: 0.002, domElement: "BODY" }
Logger.js:102:21
[INFO] Contrôles créés 
Object { keys: 0 }
Logger.js:102:21
[INFO] Création du gestionnaire d'options... 
Object {  }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[DEBUG] Mode de couleur appliqué 
Object { mode: "default", filters: [] }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[INFO] OptionsManager initialisé 
Object { settings: {…} }
Logger.js:102:21
[INFO] Gestionnaire d'options créé 
Object {  }
Logger.js:102:21
[INFO] Éclairage ajouté 
Object { sceneObjects: 3, lights: 2 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: true, isMobile: true, screenType: "mobile" }
Logger.js:102:21
[INFO] Système de redimensionnement responsif initialisé 
Object { initialSize: {…}, pixelRatio: 1, responsive: true }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179842 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
Chunk manquant détecté: (-6, 0), distance: 6.00 World.js:448:37
Chunk manquant détecté: (-5, -3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (-5, 3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (-3, -5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (-3, 5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (0, -6), distance: 6.00 World.js:448:37
Chunk manquant détecté: (0, 6), distance: 6.00 World.js:448:37
Chunk manquant détecté: (3, -5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (3, 5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (4, 4), distance: 5.66 World.js:448:37
Chunk manquant détecté: (5, -3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (5, 3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (6, 0), distance: 6.00 World.js:448:37
13 chunks manquants détectés et ajoutés à la file World.js:458:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 112, timestamp: 1179843 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 111, timestamp: 1179843 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 110, timestamp: 1179843 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 3, cameraPosition: {…}, fps: 63, frameCount: 0 }
Logger.js:102:21
[INFO] Protection contre la sélection de texte activée avec surveillance dynamique 
Object {  }
Logger.js:102:21
[INFO] Système de secours initialisé 
Object { shortcuts: {…} }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179849 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.99232 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179860 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.99195884800002 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179877 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.97527552400017 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 109, timestamp: 1179878 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 108, timestamp: 1179878 }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 0ms]

[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179893 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.95026553199993 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179910 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.91687884400008 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179927 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.87520552000035 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 107, timestamp: 1179929 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 106, timestamp: 1179929 }
Logger.js:102:21
Erreur lors de la configuration des matériaux: ReferenceError: grassSideMaterial is not defined
    loadTextures http://localhost:8000/js/world/Chunk.js:223
    initializeSharedResources http://localhost:8000/js/world/Chunk.js:87
    buildMesh http://localhost:8000/js/world/Chunk.js:617
    promises http://localhost:8000/js/world/World.js?v=1753202598917:234
    generateInitialChunks http://localhost:8000/js/world/World.js?v=1753202598917:222
    World http://localhost:8000/js/world/World.js?v=1753202598917:58
    <anonymous> http://localhost:8000/js/main.js?v=1753202598819:74
Logger.js:67:27
Matériaux de fallback chargés Chunk.js:308:17
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 1179957 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 1179965 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179965 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.82520552799988 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (1) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 1179979 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 1179986 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1179986 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.69179885200059 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (2) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 1179998 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 1180004 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 1180012 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 1180020 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1180020 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.61680885999989 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (4) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 105, timestamp: 1180021 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 104, timestamp: 1180021 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 1180037 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 1180044 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 1180045 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 1180045 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180046 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 11, visible: true, timestamp: 1180054 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180055 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 12, visible: true, timestamp: 1180061 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 1180062 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.43339218400082 }
Logger.js:102:21
[INFO] Spawn réussi 
Object { spawnPosition: {…}, groundHeight: 68, chunkKey: "0,0" }
Logger.js:102:21
[PHYSICS] Positionnement initial réussi 
Object { finalPosition: {…}, physicsEnabled: true, timestamp: 1180062 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180096 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 13, visible: true, timestamp: 1180103 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180104 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 14, visible: true, timestamp: 1180112 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-1", position: {…}, blocksCount: 32768, timestamp: 1180119 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-1", position: {…}, blocksCount: 32768, timestamp: 1180125 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,0", position: {…}, blocksCount: 32768, timestamp: 1180132 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,0", position: {…}, blocksCount: 32768, timestamp: 1180139 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 1180139 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 1180140 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 102, timestamp: 1180149 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180162 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 19, visible: true, timestamp: 1180168 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,0", position: {…}, blocksCount: 32768, timestamp: 1180188 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,0", position: {…}, blocksCount: 32768, timestamp: 1180195 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 101, timestamp: 1180196 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-1", position: {…}, blocksCount: 32768, timestamp: 1180206 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-1", position: {…}, blocksCount: 32768, timestamp: 1180213 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,2", position: {…}, blocksCount: 32768, timestamp: 1180224 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,2", position: {…}, blocksCount: 32768, timestamp: 1180231 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,1", position: {…}, blocksCount: 32768, timestamp: 1180242 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,1", position: {…}, blocksCount: 32768, timestamp: 1180249 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 1180249 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 1180250 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-2", position: {…}, blocksCount: 32768, timestamp: 1180288 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-2", position: {…}, blocksCount: 32768, timestamp: 1180295 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 1180296 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 1180296 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,2", position: {…}, blocksCount: 32768, timestamp: 1180307 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,2", position: {…}, blocksCount: 32768, timestamp: 1180314 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 1180324 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 1180335 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 1180345 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 1180352 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,-2", position: {…}, blocksCount: 32768, timestamp: 1180359 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,-2", position: {…}, blocksCount: 32768, timestamp: 1180365 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 1180366 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 1180366 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,2", position: {…}, blocksCount: 32768, timestamp: 1180400 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,2", position: {…}, blocksCount: 32768, timestamp: 1180407 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,1", position: {…}, blocksCount: 32768, timestamp: 1180418 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,1", position: {…}, blocksCount: 32768, timestamp: 1180424 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 1180433 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 1180440 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 1180453 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 1180460 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-2", position: {…}, blocksCount: 32768, timestamp: 1180470 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-2", position: {…}, blocksCount: 32768, timestamp: 1180476 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 1180476 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 1180477 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 47, totalRendered: 47 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 47, totalRendered: 47 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 1180610 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 1180610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180629 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 48, visible: true, timestamp: 1180635 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180636 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 49, visible: true, timestamp: 1180643 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 1180660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 1180660 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180670 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 50, visible: true, timestamp: 1180676 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180680 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 51, visible: true, timestamp: 1180687 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 1180710 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 1180711 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180722 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 52, visible: true, timestamp: 1180730 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180732 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 53, visible: true, timestamp: 1180739 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 1180760 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 1180761 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180770 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 54, visible: true, timestamp: 1180777 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180781 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 55, visible: true, timestamp: 1180787 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 1180810 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 1180810 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180821 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 56, visible: true, timestamp: 1180828 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180832 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 57, visible: true, timestamp: 1180838 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 1180861 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 1180861 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180871 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 58, visible: true, timestamp: 1180878 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180881 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 59, visible: true, timestamp: 1180888 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 1180910 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 74, timestamp: 1180911 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180923 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 60, visible: true, timestamp: 1180930 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180933 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 61, visible: true, timestamp: 1180940 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 73, timestamp: 1180961 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 72, timestamp: 1180961 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180972 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 62, visible: true, timestamp: 1180978 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1180982 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 63, visible: true, timestamp: 1180988 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 71, timestamp: 1181010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 70, timestamp: 1181010 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181020 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 64, visible: true, timestamp: 1181026 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181030 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 65, visible: true, timestamp: 1181036 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 69, timestamp: 1181060 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 68, timestamp: 1181060 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181071 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 66, visible: true, timestamp: 1181078 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181081 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 67, visible: true, timestamp: 1181087 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 67, timestamp: 1181109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 66, timestamp: 1181110 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181119 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 68, visible: true, timestamp: 1181126 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181127 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 69, visible: true, timestamp: 1181134 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 65, timestamp: 1181160 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 64, timestamp: 1181161 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181171 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 70, visible: true, timestamp: 1181178 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181181 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 71, visible: true, timestamp: 1181188 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 63, timestamp: 1181210 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 62, timestamp: 1181210 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181222 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 72, visible: true, timestamp: 1181230 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181233 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 73, visible: true, timestamp: 1181240 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 61, timestamp: 1181261 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 60, timestamp: 1181261 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181272 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 74, visible: true, timestamp: 1181278 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181282 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 75, visible: true, timestamp: 1181288 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 59, timestamp: 1181310 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 58, timestamp: 1181310 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181320 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 76, visible: true, timestamp: 1181327 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181331 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 77, visible: true, timestamp: 1181337 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 57, timestamp: 1181361 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 56, timestamp: 1181361 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181371 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 78, visible: true, timestamp: 1181378 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181381 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 79, visible: true, timestamp: 1181388 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 55, timestamp: 1181410 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 54, timestamp: 1181411 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181422 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 80, visible: true, timestamp: 1181429 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181432 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 81, visible: true, timestamp: 1181439 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 53, timestamp: 1181460 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 52, timestamp: 1181460 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181470 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 82, visible: true, timestamp: 1181477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181481 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 83, visible: true, timestamp: 1181486 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 51, timestamp: 1181509 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 50, timestamp: 1181510 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181521 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 84, visible: true, timestamp: 1181529 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181534 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 85, visible: true, timestamp: 1181541 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 49, timestamp: 1181560 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 48, timestamp: 1181560 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181580 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 86, visible: true, timestamp: 1181587 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181588 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 87, visible: true, timestamp: 1181594 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 47, timestamp: 1181610 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 46, timestamp: 1181610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181624 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 88, visible: true, timestamp: 1181631 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181636 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 89, visible: true, timestamp: 1181642 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 45, timestamp: 1181660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 44, timestamp: 1181660 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181671 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 90, visible: true, timestamp: 1181678 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181681 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 91, visible: true, timestamp: 1181688 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 43, timestamp: 1181710 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 42, timestamp: 1181710 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181721 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 92, visible: true, timestamp: 1181728 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181732 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 93, visible: true, timestamp: 1181738 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 41, timestamp: 1181760 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 40, timestamp: 1181761 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181773 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 94, visible: true, timestamp: 1181779 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181783 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 95, visible: true, timestamp: 1181790 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 39, timestamp: 1181810 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 38, timestamp: 1181811 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181820 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 96, visible: true, timestamp: 1181825 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181829 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 97, visible: true, timestamp: 1181840 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 37, timestamp: 1181863 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 36, timestamp: 1181863 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181873 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 98, visible: true, timestamp: 1181879 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181883 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 99, visible: true, timestamp: 1181889 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 35, timestamp: 1181911 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 34, timestamp: 1181911 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181920 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 100, visible: true, timestamp: 1181927 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181930 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 101, visible: true, timestamp: 1181937 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 33, timestamp: 1181960 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 32, timestamp: 1181961 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181970 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 102, visible: true, timestamp: 1181977 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1181981 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 103, visible: true, timestamp: 1181986 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 31, timestamp: 1182010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 30, timestamp: 1182011 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 106, cameraPosition: {…}, fps: 60, frameCount: 120 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182022 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 104, visible: true, timestamp: 1182029 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182032 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 105, visible: true, timestamp: 1182039 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 29, timestamp: 1182060 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 28, timestamp: 1182060 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182071 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 106, visible: true, timestamp: 1182077 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182081 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 107, visible: true, timestamp: 1182087 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 27, timestamp: 1182111 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 26, timestamp: 1182112 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182122 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 108, visible: true, timestamp: 1182131 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182137 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 109, visible: true, timestamp: 1182145 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 25, timestamp: 1182160 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 24, timestamp: 1182161 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182178 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 110, visible: true, timestamp: 1182184 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182188 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 111, visible: true, timestamp: 1182195 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 23, timestamp: 1182210 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 22, timestamp: 1182211 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182222 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 112, visible: true, timestamp: 1182229 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182233 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 113, visible: true, timestamp: 1182239 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 21, timestamp: 1182260 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 20, timestamp: 1182261 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182272 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 114, visible: true, timestamp: 1182279 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182283 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 115, visible: true, timestamp: 1182289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 19, timestamp: 1182311 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 18, timestamp: 1182311 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182320 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 116, visible: true, timestamp: 1182326 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182332 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 117, visible: true, timestamp: 1182338 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 17, timestamp: 1182360 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 16, timestamp: 1182361 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182370 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 118, visible: true, timestamp: 1182376 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182381 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 119, visible: true, timestamp: 1182387 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 15, timestamp: 1182410 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 14, timestamp: 1182410 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182422 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 120, visible: true, timestamp: 1182430 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182433 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 121, visible: true, timestamp: 1182440 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 13, timestamp: 1182460 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.8284271247461903, queueRemaining: 12, timestamp: 1182460 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182476 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 122, visible: true, timestamp: 1182484 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182487 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 123, visible: true, timestamp: 1182493 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 11, timestamp: 1182510 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 10, timestamp: 1182510 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182521 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 124, visible: true, timestamp: 1182529 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182532 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 125, visible: true, timestamp: 1182539 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 9, timestamp: 1182560 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 8, timestamp: 1182560 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182572 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 126, visible: true, timestamp: 1182580 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182584 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 127, visible: true, timestamp: 1182590 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 7, timestamp: 1182610 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 6, timestamp: 1182610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182621 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 128, visible: true, timestamp: 1182627 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182631 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 129, visible: true, timestamp: 1182638 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 5, timestamp: 1182660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 4, timestamp: 1182660 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182672 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 130, visible: true, timestamp: 1182679 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182683 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 131, visible: true, timestamp: 1182689 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 3, timestamp: 1182710 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 2, timestamp: 1182710 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182721 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 132, visible: true, timestamp: 1182727 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182732 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 133, visible: true, timestamp: 1182738 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 1, timestamp: 1182760 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 0, timestamp: 1182760 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182780 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 134, visible: true, timestamp: 1182786 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1182787 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 135, visible: true, timestamp: 1182794 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 1183109 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 1183111 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 1183111 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183122 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 136, visible: true, timestamp: 1183130 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183133 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 137, visible: true, timestamp: 1183141 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 1183160 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 1183160 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183170 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 138, visible: true, timestamp: 1183176 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183181 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 139, visible: true, timestamp: 1183188 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 1183210 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 1183211 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183222 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 140, visible: true, timestamp: 1183230 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183234 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 141, visible: true, timestamp: 1183240 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 1183260 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 1183260 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183271 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 142, visible: true, timestamp: 1183278 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183282 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 143, visible: true, timestamp: 1183289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 1183310 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 1183310 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183320 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 144, visible: true, timestamp: 1183326 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183331 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 145, visible: true, timestamp: 1183337 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 1183360 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 1183360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183372 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 146, visible: true, timestamp: 1183380 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183384 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 147, visible: true, timestamp: 1183394 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 1183410 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 1183411 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183422 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 148, visible: true, timestamp: 1183429 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183433 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 149, visible: true, timestamp: 1183440 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 1183460 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 1183460 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183473 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 150, visible: true, timestamp: 1183480 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183484 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 151, visible: true, timestamp: 1183491 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 1183510 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 1183511 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183522 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183523 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183524 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 152, visible: true, timestamp: 1183530 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 1183560 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 1183560 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183572 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183572 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183574 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183574 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 1183609 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 1183610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183622 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183623 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183624 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183624 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 1183660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 1183660 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183676 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183677 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183686 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183687 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 1183709 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 1183710 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183722 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183722 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183723 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183723 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 1183760 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 1183760 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183771 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183771 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183782 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183782 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 1183810 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 1183811 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183821 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183823 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183823 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 1183860 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 1183860 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183882 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183883 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183884 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183885 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 1183910 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 1183911 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183931 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183931 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183933 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183933 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 1183960 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 1183961 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183970 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183970 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1183975 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1183975 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 1184010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 1184011 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 155, cameraPosition: {…}, fps: 60, frameCount: 240 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184022 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184022 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184025 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184025 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 1184060 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 1184060 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184071 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184071 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184073 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184073 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 1184110 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 1184110 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184123 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184123 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184125 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184125 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 1184160 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 1184160 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184171 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184171 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184174 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184175 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 1184210 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 1184211 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184222 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184223 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184226 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184226 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 1184260 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 1184261 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184270 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184271 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184274 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184275 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 1184310 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 1184311 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184320 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184321 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184325 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184325 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 1184360 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 1184361 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184372 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184372 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184374 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184374 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 1184410 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 1184411 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184421 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184422 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184424 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184424 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 1184461 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 1184461 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184470 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184470 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184475 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184475 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 1184510 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 1184511 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184523 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184523 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184524 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184525 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 1184560 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 1184561 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184571 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184571 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184574 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184574 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 1184610 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 1184610 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184620 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184620 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184624 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184624 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 1184660 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 1184660 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184672 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184672 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184673 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184673 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 1184710 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 1184711 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184722 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184723 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184724 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184724 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 1184760 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 1184761 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184781 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184782 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184783 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184783 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 1184809 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 1184810 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184819 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184820 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184824 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184824 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 1184860 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 1184861 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184873 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184873 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184874 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184875 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 1184910 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 1184911 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184922 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184922 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184924 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184924 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 1184960 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 1184961 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184971 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184972 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1184973 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1184974 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 1185010 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 1185084 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 1185085 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185104 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185105 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185106 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185106 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 1185129 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 1185130 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185138 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185139 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185140 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185140 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 1185177 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 1185178 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185198 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185198 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185199 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185200 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 1185227 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 1185228 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185237 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185237 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185239 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185239 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 1185276 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 1185277 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185288 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185288 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185289 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185290 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 1185327 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 1185327 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185338 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185339 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185340 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185340 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 1185376 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 1185377 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185387 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185388 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185389 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185389 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 1185427 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 1185427 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185442 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185443 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185444 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185444 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 1185477 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 1185477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185486 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185486 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185487 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185488 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 1185526 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 1185527 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185537 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185537 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185538 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185538 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 1185577 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 1185577 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185586 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185586 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185588 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185588 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 1185626 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 1185627 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185636 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185637 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185638 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185639 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 1185676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 1185677 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185691 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185692 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185693 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185693 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 1185727 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 1185727 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185737 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185737 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185738 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185739 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 1185777 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 1185777 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185787 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185788 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185789 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 1185827 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 1185827 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185836 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185837 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185838 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185838 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 1185877 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 1185877 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185886 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185887 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185891 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185891 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 1185927 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 1185927 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185938 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185939 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185940 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185940 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 1185976 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 1185976 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185985 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185986 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1185987 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1185987 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 1186027 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 1186027 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186037 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186038 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186038 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186038 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 1186076 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 1186077 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 167, cameraPosition: {…}, fps: 60, frameCount: 360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186086 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186086 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186089 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186089 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 1186126 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 1186127 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186137 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186137 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186138 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186138 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 1186176 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 1186177 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186194 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186194 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186198 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186198 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 1186227 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 1186227 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186236 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186236 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186239 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186239 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 1186277 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 1186277 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186288 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186288 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186289 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 1186327 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 1186328 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186337 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186338 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186339 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186339 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 1186377 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 1186377 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186394 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186397 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186398 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 1186427 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 1186427 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186438 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186439 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186440 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186440 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 1186476 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 1186477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186486 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186487 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186488 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186489 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 1186527 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 1186527 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186538 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186538 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186547 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186547 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 1186577 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 1186577 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186586 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186587 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186589 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186589 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 1186627 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 1186627 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186645 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186645 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186648 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186648 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 1186676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 1186677 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186687 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186687 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186688 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186689 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 1186727 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 1186727 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186737 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186738 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186739 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186739 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 1186777 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 1186777 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186787 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186788 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186789 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186789 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 1186827 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 1186828 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186836 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186836 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186838 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186838 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 1186877 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 1186878 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186888 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186889 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186890 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186890 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 1186927 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 1186928 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186938 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186938 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186939 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186939 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 1186977 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 1186978 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186987 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186988 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1186989 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1186990 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 1187027 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 1187027 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187038 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187038 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187039 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187040 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 1187077 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 1187078 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187087 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187087 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187089 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187089 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 1187126 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 1187127 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187136 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187137 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187138 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187139 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 1187177 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 1187177 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187188 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187188 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187189 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187189 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 1187227 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 1187227 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187236 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187237 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187238 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187238 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 1187276 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 1187277 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187288 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187289 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187290 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187290 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 1187327 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 1187327 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187337 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187337 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187339 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187339 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 1187376 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 1187377 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187387 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187387 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187388 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187389 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 1187426 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 1187427 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187437 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187437 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187438 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187439 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 1187476 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 1187477 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187489 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187489 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187492 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187492 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 1187527 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 1187527 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187539 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187539 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1187540 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1187540 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 167, cameraPosition: {…}, fps: 60, frameCount: 480 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 1188227 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 1188299 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 1188299 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188317 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188317 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188318 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188318 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 1188343 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 1188343 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188353 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188353 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188355 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188355 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 1188393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 1188394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188403 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188403 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188404 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188404 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 1188443 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 1188444 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188454 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188455 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188455 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188456 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 1188493 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 1188494 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188504 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188504 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188505 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188505 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 1188543 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 1188544 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188553 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188553 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188554 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188554 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 1188594 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 1188594 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188604 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188604 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188605 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188605 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 1188644 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 1188644 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188654 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188654 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188655 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188655 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 1188693 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 1188694 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188703 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188703 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188705 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188705 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 1188744 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 1188744 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188755 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188756 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188757 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188757 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 1188794 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 1188794 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188803 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188803 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188805 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188805 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 1188843 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 1188844 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188853 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188854 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188855 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188855 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 1188893 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 1188894 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188903 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188903 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188904 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188904 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 1188943 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 1188944 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188954 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188954 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1188955 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1188955 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 1188994 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 1188994 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189013 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189014 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189015 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189016 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 1189043 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 1189044 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189054 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189054 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189055 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189055 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 1189093 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 1189094 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189104 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189104 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189105 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189105 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 1189144 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 1189144 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189153 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189153 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189154 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189155 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 1189193 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 1189194 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189204 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189204 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189205 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189205 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 1189244 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 1189245 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189254 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189254 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189255 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189255 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 1189293 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 1189294 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189303 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189303 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189305 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189305 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 1189344 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 1189344 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189354 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189354 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189356 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189356 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 1189393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 1189394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189403 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189403 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189404 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189405 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 1189443 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 1189444 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189454 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189455 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189456 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189456 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 1189493 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 1189493 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189504 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189504 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189506 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189506 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 1189543 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 1189544 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189557 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189558 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189560 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189560 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 1189593 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 1189594 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189605 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189605 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189608 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189608 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 1189644 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 1189644 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189658 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189658 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189660 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189661 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 1189694 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 1189694 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189707 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189707 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189709 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189709 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 1189743 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 1189743 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189767 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189768 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189770 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189771 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 1189793 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 1189794 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189817 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189820 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189822 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189823 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 1189844 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 1189845 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189868 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189869 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189871 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189872 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 1189893 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 1189894 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189915 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189916 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189917 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189918 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 1189943 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 1189944 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189955 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189955 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1189957 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1189957 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 1189993 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 1189993 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190016 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190016 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190018 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190018 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 1190043 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 1190044 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190061 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190061 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190066 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190067 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 1190093 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 1190094 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190115 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190115 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190116 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190116 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 1190143 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 1190144 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 179, cameraPosition: {…}, fps: 60, frameCount: 600 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190157 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190158 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190160 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190160 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 1190193 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 1190194 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190206 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190207 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190209 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190209 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 1190243 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 1190243 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190266 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190266 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190269 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190269 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 1190294 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 1190295 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190307 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190308 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190309 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190309 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 1190343 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 1190343 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190354 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190354 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190356 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190356 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 1190393 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 1190394 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190406 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190407 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190409 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190409 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 1190443 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 1190444 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190455 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190456 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190457 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190457 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 1190493 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 1190494 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190503 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190504 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190505 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190505 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 1190544 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 1190544 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190553 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190553 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190554 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190555 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 1190594 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 1190594 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190606 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190606 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190607 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190607 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 1190643 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 1190644 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190653 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190654 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190655 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190655 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 1190694 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 1190694 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190703 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190704 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190706 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190706 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 1190743 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 1190744 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190754 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190755 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 1190756 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 1190756 }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 179, cameraPosition: {…}, fps: 60, frameCount: 720 }
Logger.js:102:21
