📄 Logs sauvegardés: minecraft-js-logs-session-2025-07-22T18-12-55-772Z-j91s39.html (1000 entrées) Logger.js:193:17
La demande de verrouillage du pointeur a été refusée, car Element.requestPointerLock() n’a pas été appelé depuis un gestionnaire d’évènement généré par l’utilisateur et que le document n’est pas en plein écran. localhost:8000
GET
http://localhost:8000/
[HTTP/1 304 Not Modified 62ms]

GET
http://localhost:8000/css/style.css
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/css/mining-ui.css
[HTTP/1 200 OK 0ms]

GET
https://unpkg.com/three@0.160.0/build/three.min.js
[HTTP/2 200  0ms]

Scripts "build/three.js" and "build/three.min.js" are deprecated with r150+, and will be removed with r160. Please use ES Modules or alternatives: https://threejs.org/docs/index.html#manual/en/introduction/Installation three.min.js:1:9
🔄 CACHE BUSTER ACTIVÉ - Timestamp: 1753208158569 localhost:8000:171:17
🎨 CSS chargé avec cache-buster: css/style.css?v=1753208158569 localhost:8000:187:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/version.js?v=1753208158569 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SmartLogger.js?v=1753208158569 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/utils/SimplexNoise.js?v=1753208158569 localhost:8000:179:21
📦 Script chargé avec cache-buster: http://localhost:8000/js/main.js?v=1753208158569 localhost:8000:179:21
GET
http://localhost:8000/css/style.css?v=1753208158569
[HTTP/1 200 OK 3ms]

GET
http://localhost:8000/js/version.js?v=1753208158569
[HTTP/1 200 OK 2ms]

GET
http://localhost:8000/js/utils/SmartLogger.js?v=1753208158569
[HTTP/1 200 OK 3ms]

GET
http://localhost:8000/js/utils/SimplexNoise.js?v=1753208158569
[HTTP/1 200 OK 3ms]

GET
http://localhost:8000/js/main.js?v=1753208158569
[HTTP/1 200 OK 3ms]

Jeu de règles ignoré suite à un mauvais sélecteur. style.css:480:43
🚀 ===== MINECRAFT JS - v1753208158622 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-22T18:15:58.622Z version.js:26:17
🔄 Cache Buster: 1753208158622 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753208158622 version.js:30:21
   ✅ WORLD: World-v1753208158622 version.js:30:21
   ✅ CHUNK: Chunk-v1753208158622 version.js:30:21
   ✅ CONTROLS: Controls-v1753208158622 version.js:30:21
   ✅ MAIN: Main-v1753208158622 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies version.js:45:17
GET
http://localhost:8000/favicon.ico
[HTTP/1 404 File not found 0ms]

GET
http://localhost:8000/js/version.js
[HTTP/1 200 OK 0ms]

🚀 ===== MINECRAFT JS - v1753208158653 ===== version.js:25:17
📅 Dernière mise à jour: 2025-07-22T18:15:58.653Z version.js:26:17
🔄 Cache Buster: 1753208158653 version.js:27:17
📦 Modules chargés: version.js:28:17
   ✅ PLAYER: Player-v1753208158653 version.js:30:21
   ✅ WORLD: World-v1753208158653 version.js:30:21
   ✅ CHUNK: Chunk-v1753208158653 version.js:30:21
   ✅ CONTROLS: Controls-v1753208158653 version.js:30:21
   ✅ MAIN: Main-v1753208158653 version.js:30:21
🛡️ Protection anti-cache: ACTIVÉE version.js:32:17
=============================================== version.js:33:17
🛡️ Auto-reload DÉSACTIVÉ pour éviter les boucles infinies 2 version.js:45:17
📦 Import avec cache-buster: ./utils/Logger.js?v=1753208158653 main.js:10:13
GET
http://localhost:8000/js/utils/Logger.js?v=1753208158653
[HTTP/1 200 OK 2ms]

[SYSTEM] Logger initialized 
Object { sessionId: "session-2025-07-22T18-15-58-663Z-oq2oat", timestamp: "2025-07-22T18:15:58.663Z", userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0", url: "http://localhost:8000/" }
Logger.js:102:21
🔍 Logger initialisé - Session: session-2025-07-22T18-15-58-663Z-oq2oat Logger.js:19:17
📦 Import avec cache-buster: ./utils/TextureGenerator.js?v=1753208158653 main.js:10:13
GET
http://localhost:8000/js/utils/TextureGenerator.js?v=1753208158653
[HTTP/1 200 OK 3ms]

📦 Import avec cache-buster: ./world/World.js?v=1753208158653 main.js:10:13
GET
http://localhost:8000/js/world/World.js?v=1753208158653
[HTTP/1 200 OK 1ms]

GET
http://localhost:8000/js/world/Chunk.js
[HTTP/1 200 OK 0ms]

GET
http://localhost:8000/js/world/WorldGenerator.js
[HTTP/1 304 Not Modified 2ms]

GET
http://localhost:8000/js/utils/WorkerManager.js
[HTTP/1 200 OK 0ms]

📦 Import avec cache-buster: ./player/Player.js?v=1753208158653 main.js:10:13
GET
http://localhost:8000/js/player/Player.js?v=1753208158653
[HTTP/1 200 OK 1ms]

📦 Import avec cache-buster: ./player/Controls.js?v=1753208158653 main.js:10:13
GET
http://localhost:8000/js/player/Controls.js?v=1753208158653
[HTTP/1 200 OK 3ms]

📦 Import avec cache-buster: ./ui/OptionsManager.js?v=1753208158653 main.js:10:13
GET
http://localhost:8000/js/ui/OptionsManager.js?v=1753208158653
[HTTP/1 200 OK 2ms]

[INFO] Tous les modules importés avec succès 
Object { version: "v1753208158653", cacheBuster: 1753208158653 }
Logger.js:102:21
[INFO] Three.js chargé 
Object { revision: "160" }
Logger.js:102:21
[INFO] Modules de génération de terrain chargés 
Object {  }
Logger.js:102:21
[INFO] Renderer créé 
Object { size: {…}, pixelRatio: 1, canvas: "game" }
Logger.js:102:21
[INFO] Création du générateur de textures... 
Object {  }
Logger.js:102:21
[INFO] TextureGenerator initialisé 
Object { blockTypes: 15, textureSize: 16 }
Logger.js:102:21
[INFO] Générateur de textures créé 
Object {  }
Logger.js:102:21
[INFO] Création du monde... 
Object {  }
Logger.js:102:21
[INFO] Système de monde chargé 
Object { version: "World-v1753208158902", features: {…} }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 154361 }
Logger.js:102:21
[INFO] Génération des chunks initiaux... 
Object {  }
Logger.js:102:21
[CHUNK] Préparation des chunks initiaux 
Object { totalChunks: 25, radius: 2, batchSize: 5, timestamp: 154362 }
Logger.js:102:21
[INFO] Monde créé 
Object { chunks: 0 }
Logger.js:102:21
[INFO] Création du joueur... 
Object {  }
Logger.js:102:21
[INFO] Player initialisé 
Object { version: "Player-v1753208158910", features: {…} }
Logger.js:102:21
[INFO] Caméra initialisée 
Object { position: {…}, rotation: {…} }
Logger.js:102:21
[INFO] Joueur créé 
Object { position: {…}, flyMode: false }
Logger.js:102:21
[INFO] Création des contrôles... 
Object {  }
Logger.js:102:21
[INFO] Contrôles initialisés 
Object { sensitivity: 0.002, domElement: "BODY" }
Logger.js:102:21
[INFO] Contrôles créés 
Object { keys: 0 }
Logger.js:102:21
[INFO] Création du gestionnaire d'options... 
Object {  }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[DEBUG] Mode de couleur appliqué 
Object { mode: "default", filters: [] }
Logger.js:102:21
[DEBUG] Paramètres de couleur appliqués 
Object { filters: (8) […], hdrMode: false, colorCorrection: true }
Logger.js:102:21
[INFO] OptionsManager initialisé 
Object { settings: {…} }
Logger.js:102:21
[INFO] Gestionnaire d'options créé 
Object {  }
Logger.js:102:21
[INFO] Éclairage ajouté 
Object { sceneObjects: 3, lights: 2 }
Logger.js:102:21
[DEBUG] Interface responsive mise à jour 
Object { isSmallScreen: true, isMobile: true, screenType: "mobile" }
Logger.js:102:21
[INFO] Système de redimensionnement responsif initialisé 
Object { initialSize: {…}, pixelRatio: 1, responsive: true }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154373 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
Chunk manquant détecté: (-6, 0), distance: 6.00 World.js:448:37
Chunk manquant détecté: (-5, -3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (-5, 3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (-3, -5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (-3, 5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (0, -6), distance: 6.00 World.js:448:37
Chunk manquant détecté: (0, 6), distance: 6.00 World.js:448:37
Chunk manquant détecté: (3, -5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (3, 5), distance: 5.83 World.js:448:37
Chunk manquant détecté: (4, 4), distance: 5.66 World.js:448:37
Chunk manquant détecté: (5, -3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (5, 3), distance: 5.83 World.js:448:37
Chunk manquant détecté: (6, 0), distance: 6.00 World.js:448:37
13 chunks manquants détectés et ajoutés à la file World.js:458:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 112, timestamp: 154375 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 111, timestamp: 154375 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 110, timestamp: 154375 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 3, cameraPosition: {…}, fps: 63, frameCount: 0 }
Logger.js:102:21
[INFO] Protection contre la sélection de texte activée avec surveillance dynamique 
Object {  }
Logger.js:102:21
[INFO] Système de secours initialisé 
Object { shortcuts: {…} }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154379 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 100 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154384 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.99232 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154401 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.9763264 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 109, timestamp: 154404 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 108, timestamp: 154404 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154418 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.952006132 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154435 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.919320004 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154451 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.878336404 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 107, timestamp: 154453 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 106, timestamp: 154453 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154468 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.82902613599998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154484 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.771320008 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154502 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.70534640799998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 105, timestamp: 154503 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 104, timestamp: 154504 }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 304 Not Modified 2ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 5ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 7ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 6ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 8ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 8ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 9ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 12ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 10ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 14ms]

GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 18ms]

[PHYSICS] Début du positionnement initial 
Object { timestamp: 154518 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.63104613999997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
GET
http://localhost:8000/js/workers/ChunkWorker.js
[HTTP/1 200 OK 17ms]

[PHYSICS] Début du positionnement initial 
Object { timestamp: 154534 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.548320012 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154551 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.45735641199998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 103, timestamp: 154553 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 102, timestamp: 154558 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154568 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.35806614399996 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154585 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.250320016 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154601 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.13436641599998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 101, timestamp: 154602 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 100, timestamp: 154603 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154618 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 99.01008614799996 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: [] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154654 }
Logger.js:102:21
Erreur lors de la configuration des matériaux: ReferenceError: grassSideMaterial is not defined
    loadTextures http://localhost:8000/js/world/Chunk.js:223
    initializeSharedResources http://localhost:8000/js/world/Chunk.js:87
    buildMesh http://localhost:8000/js/world/Chunk.js:616
    generateChunksInQueue http://localhost:8000/js/world/World.js?v=1753208158653:332
    promise callback*generateChunksInQueue http://localhost:8000/js/world/World.js?v=1753208158653:314
    update http://localhost:8000/js/world/World.js?v=1753208158653:124
    animate http://localhost:8000/js/main.js?v=1753208158569:292
    animate http://localhost:8000/js/main.js?v=1753208158569:274
    animate http://localhost:8000/js/main.js?v=1753208158569:274
    animate http://localhost:8000/js/main.js?v=1753208158569:274
    animate http://localhost:8000/js/main.js?v=1753208158569:274
    animate http://localhost:8000/js/main.js?v=1753208158569:274
    animate http://localhost:8000/js/main.js?v=1753208158569:274
    <anonymous> http://localhost:8000/js/main.js?v=1753208158569:416
Logger.js:67:27
Matériaux de fallback chargés Chunk.js:308:17
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 1, visible: true, timestamp: 154674 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154675 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.87732002000001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (1) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154681 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 2, visible: true, timestamp: 154692 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154693 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.45432002000001 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (2) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 154694 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 154695 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 154738 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,1", position: {…}, blocksCount: 32768, timestamp: 154746 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154747 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 98.28838641999998 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (3) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154754 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 5, visible: true, timestamp: 154763 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154764 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 6, visible: true, timestamp: 154771 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154771 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.76539641999997 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (5) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 154782 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-1,0", position: {…}, blocksCount: 32768, timestamp: 154791 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 154799 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,-1", position: {…}, blocksCount: 32768, timestamp: 154811 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154812 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 97.366656688 }
Logger.js:102:21
[DEBUG] Chunk de spawn pas encore généré 
Object { chunkKey: "0,0", availableChunks: (7) […] }
Logger.js:102:21
[WARN] Positionnement initial échoué 
Object { reason: "Chunk pas encore généré", action: "Attente de génération des chunks" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 154813 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 154813 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 154829 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "0,0", position: {…}, blocksCount: 32768, timestamp: 154837 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154839 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 13, visible: true, timestamp: 154846 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154848 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 14, visible: true, timestamp: 154856 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154858 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 15, visible: true, timestamp: 154865 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 154873 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,0", position: {…}, blocksCount: 32768, timestamp: 154879 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 154879 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 5, timestamp: 154879 }
Logger.js:102:21
[PHYSICS] Début du positionnement initial 
Object { timestamp: 154880 }
Logger.js:102:21
[DEBUG] Recherche position de spawn 
Object { targetPosition: {…}, currentY: 96.93482948799992 }
Logger.js:102:21
[INFO] Spawn réussi 
Object { spawnPosition: {…}, groundHeight: 68, chunkKey: "0,0" }
Logger.js:102:21
[PHYSICS] Positionnement initial réussi 
Object { finalPosition: {…}, physicsEnabled: true, timestamp: 154880 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154895 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 18, visible: true, timestamp: 154901 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154903 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 19, visible: true, timestamp: 154910 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154912 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 20, visible: true, timestamp: 154919 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 154920 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 10, timestamp: 154920 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154921 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 21, visible: true, timestamp: 154927 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 154929 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 22, visible: true, timestamp: 154937 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 154937 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 15, timestamp: 154937 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 154951 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 154952 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 154973 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,2", position: {…}, blocksCount: 32768, timestamp: 154979 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-2", position: {…}, blocksCount: 32768, timestamp: 154990 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "1,-2", position: {…}, blocksCount: 32768, timestamp: 154996 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 155004 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-1", position: {…}, blocksCount: 32768, timestamp: 155010 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 155011 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 20, timestamp: 155011 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 155017 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155046 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 29, visible: true, timestamp: 155054 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,1", position: {…}, blocksCount: 32768, timestamp: 155061 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,1", position: {…}, blocksCount: 32768, timestamp: 155067 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 155081 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,2", position: {…}, blocksCount: 32768, timestamp: 155088 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 155088 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 155089 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 155099 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "2,-2", position: {…}, blocksCount: 32768, timestamp: 155105 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-2", position: {…}, blocksCount: 32768, timestamp: 155114 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,-2", position: {…}, blocksCount: 32768, timestamp: 155120 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,2", position: {…}, blocksCount: 32768, timestamp: 155134 }
Logger.js:102:21
[CHUNK] Chunk généré avec succès 
Object { chunkKey: "-2,2", position: {…}, blocksCount: 32768, timestamp: 155141 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 155141 }
Logger.js:102:21
[CHUNK] Lot de chunks généré 
Object { successCount: 5, totalInBatch: 5, processedTotal: 25, timestamp: 155141 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 39, totalRendered: 39 }
Logger.js:102:21
[INFO] Génération de chunks initiaux terminée 
Object { totalGenerated: 39, totalRendered: 39 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 155202 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 155202 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155216 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 40, visible: true, timestamp: 155223 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155227 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 41, visible: true, timestamp: 155234 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 155251 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 155252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155263 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 42, visible: true, timestamp: 155271 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155274 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 43, visible: true, timestamp: 155280 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 155301 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 155301 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155316 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 44, visible: true, timestamp: 155325 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155328 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 45, visible: true, timestamp: 155335 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 155351 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 155352 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 155353 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155365 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 46, visible: true, timestamp: 155373 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155376 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 47, visible: true, timestamp: 155382 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 155401 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 155401 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155414 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 48, visible: true, timestamp: 155421 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155424 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 49, visible: true, timestamp: 155431 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 155451 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 155452 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155465 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 50, visible: true, timestamp: 155473 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155478 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 51, visible: true, timestamp: 155487 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 155502 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 155502 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155517 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 52, visible: true, timestamp: 155525 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155529 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 53, visible: true, timestamp: 155536 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 155552 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 155552 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155565 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 54, visible: true, timestamp: 155573 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155577 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 55, visible: true, timestamp: 155583 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 155602 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 155602 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155615 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 56, visible: true, timestamp: 155623 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155626 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 57, visible: true, timestamp: 155632 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 155651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 155652 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155666 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 58, visible: true, timestamp: 155674 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155679 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 59, visible: true, timestamp: 155687 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 155701 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 155701 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155716 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 60, visible: true, timestamp: 155724 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155727 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 61, visible: true, timestamp: 155736 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 155751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 155752 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155763 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 62, visible: true, timestamp: 155770 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155773 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 63, visible: true, timestamp: 155780 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 155801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 155802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155815 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 64, visible: true, timestamp: 155822 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155826 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 65, visible: true, timestamp: 155833 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 155851 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 155852 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155865 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 66, visible: true, timestamp: 155871 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155875 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 67, visible: true, timestamp: 155882 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 155902 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 155903 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155923 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 68, visible: true, timestamp: 155929 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155930 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 69, visible: true, timestamp: 155938 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 155951 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 74, timestamp: 155951 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155964 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 70, visible: true, timestamp: 155971 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 155975 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 71, visible: true, timestamp: 155981 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 73, timestamp: 156001 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 72, timestamp: 156001 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156015 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 72, visible: true, timestamp: 156022 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156026 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 73, visible: true, timestamp: 156032 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 71, timestamp: 156051 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 70, timestamp: 156052 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156064 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 74, visible: true, timestamp: 156070 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156074 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 75, visible: true, timestamp: 156080 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 69, timestamp: 156101 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 68, timestamp: 156102 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156115 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 76, visible: true, timestamp: 156122 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156126 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 77, visible: true, timestamp: 156132 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 67, timestamp: 156152 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 66, timestamp: 156152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156165 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 78, visible: true, timestamp: 156172 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156175 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 79, visible: true, timestamp: 156181 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 65, timestamp: 156201 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 64, timestamp: 156201 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156213 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 80, visible: true, timestamp: 156220 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 12, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753208160766, timestamp: 156221 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156224 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 81, visible: true, timestamp: 156231 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 63, timestamp: 156251 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 62, timestamp: 156251 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156263 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 82, visible: true, timestamp: 156269 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156274 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 83, visible: true, timestamp: 156280 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 61, timestamp: 156301 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 60, timestamp: 156302 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156317 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 84, visible: true, timestamp: 156324 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156328 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 85, visible: true, timestamp: 156335 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 59, timestamp: 156351 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 58, timestamp: 156352 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156365 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 86, visible: true, timestamp: 156374 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156378 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 87, visible: true, timestamp: 156384 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 57, timestamp: 156401 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 56, timestamp: 156402 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156414 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 88, visible: true, timestamp: 156422 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156426 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 89, visible: true, timestamp: 156432 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 156451 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 99, timestamp: 156452 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 98, timestamp: 156453 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156465 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 90, visible: true, timestamp: 156472 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156477 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 91, visible: true, timestamp: 156487 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 97, timestamp: 156501 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 96, timestamp: 156502 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156515 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 92, visible: true, timestamp: 156522 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156525 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 93, visible: true, timestamp: 156532 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 95, timestamp: 156551 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 94, timestamp: 156552 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156563 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 94, visible: true, timestamp: 156573 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156576 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 95, visible: true, timestamp: 156583 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 93, timestamp: 156601 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 92, timestamp: 156602 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 98, cameraPosition: {…}, fps: 60, frameCount: 120 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156614 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 96, visible: true, timestamp: 156621 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156624 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 97, visible: true, timestamp: 156631 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 91, timestamp: 156652 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 90, timestamp: 156652 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156665 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 98, visible: true, timestamp: 156672 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156676 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 156676 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 89, timestamp: 156701 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 88, timestamp: 156702 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156713 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 156714 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156715 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 99, visible: true, timestamp: 156723 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 87, timestamp: 156751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 86, timestamp: 156752 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156772 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 100, visible: true, timestamp: 156778 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156779 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 101, visible: true, timestamp: 156786 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 85, timestamp: 156801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 84, timestamp: 156801 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156814 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 102, visible: true, timestamp: 156821 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156824 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 103, visible: true, timestamp: 156831 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 83, timestamp: 156851 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 82, timestamp: 156854 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156865 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 104, visible: true, timestamp: 156880 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156884 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 156885 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 81, timestamp: 156902 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 80, timestamp: 156902 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156914 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 105, visible: true, timestamp: 156921 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156925 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 156926 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 79, timestamp: 156952 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 78, timestamp: 156953 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156964 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 106, visible: true, timestamp: 156970 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 156975 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 107, visible: true, timestamp: 156981 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 77, timestamp: 157002 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 76, timestamp: 157002 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157015 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157015 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157016 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 108, visible: true, timestamp: 157023 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 75, timestamp: 157052 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.8284271247461903, queueRemaining: 74, timestamp: 157052 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157064 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157064 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157065 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 109, visible: true, timestamp: 157072 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 73, timestamp: 157101 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 72, timestamp: 157102 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157114 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 110, visible: true, timestamp: 157121 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157125 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157126 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 71, timestamp: 157151 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 70, timestamp: 157151 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157164 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157164 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157165 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 111, visible: true, timestamp: 157173 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 69, timestamp: 157202 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 68, timestamp: 157202 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157214 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 112, visible: true, timestamp: 157221 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157225 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157225 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 67, timestamp: 157251 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2.9154759474226504, queueRemaining: 66, timestamp: 157252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157264 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157264 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157265 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 113, visible: true, timestamp: 157271 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 65, timestamp: 157302 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 64, timestamp: 157302 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157313 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 114, visible: true, timestamp: 157320 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157324 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157324 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 63, timestamp: 157352 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 3, queueRemaining: 62, timestamp: 157352 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157363 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157364 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157365 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157366 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 61, timestamp: 157404 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 60, timestamp: 157405 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157417 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 115, visible: true, timestamp: 157424 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157427 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 116, visible: true, timestamp: 157434 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 59, timestamp: 157451 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 58, timestamp: 157451 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157469 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 117, visible: true, timestamp: 157475 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157479 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 118, visible: true, timestamp: 157485 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 57, timestamp: 157503 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 56, timestamp: 157503 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157524 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 119, visible: true, timestamp: 157530 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157531 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157532 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 55, timestamp: 157551 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 54, timestamp: 157551 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157566 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157566 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157567 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157568 }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 53, timestamp: 157602 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 52, timestamp: 157602 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157615 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 120, visible: true, timestamp: 157623 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157626 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 121, visible: true, timestamp: 157633 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 51, timestamp: 157651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 50, timestamp: 157652 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157672 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157672 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157672 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157673 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 49, timestamp: 157701 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 48, timestamp: 157701 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157714 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157714 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157715 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157715 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 47, timestamp: 157751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 46, timestamp: 157751 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157764 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 122, visible: true, timestamp: 157771 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157774 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157775 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 45, timestamp: 157801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 157802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157814 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157815 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157816 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157816 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 43, timestamp: 157851 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 42, timestamp: 157852 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157863 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 123, visible: true, timestamp: 157871 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157875 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 124, visible: true, timestamp: 157881 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 41, timestamp: 157901 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 40, timestamp: 157906 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157918 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 125, visible: true, timestamp: 157925 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157931 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 126, visible: true, timestamp: 157939 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 1, queueRemaining: 39, timestamp: 157951 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 157951 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157964 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 157964 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 157965 }
Logger.js:102:21
[CHUNK] Chunk ajouté à la scène 
Object { position: {…}, totalRendered: 127, visible: true, timestamp: 157974 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 158002 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 158003 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158015 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158015 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158017 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158017 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 158051 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 158052 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158064 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158065 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158066 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158067 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 158101 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 158102 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158114 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158115 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158116 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158116 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 158151 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 158152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158164 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158164 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158166 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158166 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 158201 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 158202 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158222 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158222 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158224 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158224 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 158251 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 158252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158267 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158268 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 158271 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 158272 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 159720 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 159721 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159740 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159740 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159742 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159742 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 159768 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 159768 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159785 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159785 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159790 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159790 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 159818 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 159819 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159831 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159831 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159833 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159833 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 159868 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 159868 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159891 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159891 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159894 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159894 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 159918 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 159918 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159931 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159932 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159933 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159934 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 159968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 159969 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159983 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159983 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 159984 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 159985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 160017 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 160018 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 130, cameraPosition: {…}, fps: 60, frameCount: 240 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160033 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160034 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160039 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160040 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 160068 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 160068 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160081 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160082 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160085 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160086 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 160118 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 160118 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160131 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160131 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160132 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160133 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 160168 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 160168 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160180 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160180 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160181 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160181 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 160217 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 160218 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160232 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160232 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160234 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160235 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 160268 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 160269 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160281 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160281 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160282 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160283 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 160318 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 160319 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160341 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160342 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160343 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160343 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 160618 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 160700 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 160700 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160718 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160718 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160720 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160721 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 160751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 160751 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160763 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160763 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160764 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160766 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 160801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 160802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160814 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160815 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160816 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160816 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 160851 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 160852 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160864 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160864 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160865 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160865 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 160901 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 160901 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160921 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160922 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160923 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160923 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 160951 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 160952 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160965 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160966 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 160967 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 160967 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 161001 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 161001 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161014 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161014 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161016 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161016 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 161051 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 161052 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161062 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161063 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161064 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161065 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 161101 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 161102 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161115 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161115 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161117 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161117 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 161151 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 161152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161164 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161164 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161166 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161166 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 161201 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 161202 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161214 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161214 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161215 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161216 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 161252 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 161252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161263 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161264 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161265 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161265 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 161301 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 161301 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161313 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161314 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161315 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161315 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 161351 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 161352 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161372 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161372 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161374 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161374 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 161401 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 161402 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161414 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161415 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161416 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161416 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 161451 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 161452 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161464 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161465 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161466 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161467 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 161502 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 161502 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161517 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161517 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161519 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161519 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 161551 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 161551 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161565 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161566 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161569 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161569 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 161602 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 161602 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161622 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161622 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161624 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161624 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 161651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 161651 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161664 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161665 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161672 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161673 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 161701 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 161702 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161714 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161714 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161715 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161716 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 161751 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 161752 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161763 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161763 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161765 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161765 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 161801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 161802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161815 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161815 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161816 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161816 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 161851 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 161852 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161866 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161866 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161867 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161868 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 161901 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 161902 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161913 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161914 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161915 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161915 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 161951 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 161951 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161965 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161965 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 161965 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 161966 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 162001 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 162002 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162015 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162015 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162017 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162017 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 162051 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 162052 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162063 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162064 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162066 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162066 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 162102 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 162102 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 143, cameraPosition: {…}, fps: 60, frameCount: 360 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162114 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162114 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162115 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162116 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 162151 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 162152 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162164 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162166 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162166 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162167 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 162201 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 162201 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162213 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162214 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162215 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162215 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 162251 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 162252 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162264 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162265 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162266 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162266 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 162301 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 162301 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162313 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162314 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162315 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162316 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 162351 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 162351 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162364 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162365 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162366 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162366 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 162401 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 162402 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162424 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162424 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162425 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162426 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 162451 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 162452 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162463 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162464 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162464 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162466 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 162501 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 162501 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162512 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162513 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162515 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162515 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 162552 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 162552 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162563 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162563 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162565 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162565 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 162601 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 162602 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162613 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162614 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162615 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162616 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 162651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 162651 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162663 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162664 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162666 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162666 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 162701 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 162779 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 162779 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162799 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162800 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162801 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162801 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 162835 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 162835 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162855 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162856 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162857 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162857 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 162884 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 162885 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162896 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162897 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162898 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162898 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 162934 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 162936 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162949 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162950 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 162957 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 162957 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 162985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 163034 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 163034 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163048 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163048 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163049 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163050 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 163070 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 163070 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163082 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163083 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163085 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163085 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 163118 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 163118 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163131 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163132 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163133 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163133 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 163168 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 163168 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163180 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163180 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163181 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163181 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 163217 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 163219 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163230 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163230 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163231 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163232 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 163267 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 163268 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163287 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163288 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163289 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163289 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 163319 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 163319 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163330 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163331 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163332 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163332 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 163368 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 163369 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163380 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163381 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163382 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163382 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 163418 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 163419 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163432 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163432 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163433 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163433 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 163468 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 163468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163479 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163479 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163481 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163481 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 12, wasOnGround: true, flyMode: false, cooldownRemaining: 200, jumpStartTime: 1753208168030, timestamp: 163485 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 163517 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 163518 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163529 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163530 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163531 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163531 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 163568 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 163568 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163580 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163580 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163584 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163584 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 163618 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 163618 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163631 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163631 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163632 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163633 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 163667 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 163668 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163683 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163684 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163685 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163686 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 163718 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 163718 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163729 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163730 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163731 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163731 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 163768 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 163769 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163780 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163781 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163782 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163782 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 163818 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 163819 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163831 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163832 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163833 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163833 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 163868 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 163869 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163880 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163881 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163882 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163882 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 163919 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 163919 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163932 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163932 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163933 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163934 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 163968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 163968 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163980 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163980 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 163981 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 163982 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 164019 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 164019 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164030 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164030 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164032 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164032 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 164068 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 164068 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164081 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164081 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164083 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164083 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 164118 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 164119 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164139 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164139 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164140 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164140 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 164168 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 164168 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164179 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164181 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164182 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164182 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 164218 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 164218 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 164, cameraPosition: {…}, fps: 60, frameCount: 480 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164231 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164232 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164233 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164233 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 164268 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 164268 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164281 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164282 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164284 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164284 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 164318 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 164318 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164331 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164331 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164332 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164333 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 164367 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 164368 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164380 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164381 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164382 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164383 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 164418 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 164418 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164430 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164430 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164431 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164432 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 164468 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 164468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164491 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164492 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164493 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164493 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 164518 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 164518 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164530 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164530 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164531 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164531 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 164568 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 164568 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164579 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164580 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164581 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164581 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 164617 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 164618 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164630 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164632 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164633 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164633 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 164668 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 164669 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164680 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164680 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164682 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164682 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 164718 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 164718 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164730 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164730 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164731 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164732 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 164768 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 164768 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164784 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164784 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164787 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164788 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 164817 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 164818 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164831 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164831 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164833 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164833 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 164868 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 99, timestamp: 164936 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 98, timestamp: 164937 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164955 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164955 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164956 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164957 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 97, timestamp: 164985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 96, timestamp: 164985 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164997 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164998 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 164998 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 164998 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 95, timestamp: 165034 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 94, timestamp: 165035 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165049 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165050 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165051 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165052 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 93, timestamp: 165085 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 92, timestamp: 165085 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165098 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165098 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165099 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165099 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 91, timestamp: 165135 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 90, timestamp: 165135 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165146 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165147 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165148 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165148 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 89, timestamp: 165185 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 88, timestamp: 165185 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165196 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165197 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165198 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165198 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 87, timestamp: 165235 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 86, timestamp: 165235 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165248 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165248 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165249 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165249 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 85, timestamp: 165284 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 165285 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165297 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165298 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165299 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165299 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 165335 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 165335 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165347 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165348 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165348 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165349 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 165385 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 165385 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165396 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165396 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165397 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165397 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 165435 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 165435 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165446 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165447 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165448 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165448 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 165484 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 165484 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165496 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165497 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165502 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165502 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 165534 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 165534 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165547 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165547 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165549 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165549 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 165585 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 165585 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165596 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165597 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165598 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165598 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 165634 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 165635 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165647 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165647 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165649 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165650 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 165684 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 165684 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165698 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165698 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165701 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165702 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 165735 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 165736 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165747 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165747 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165748 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165749 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 165784 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 165785 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165797 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165797 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165799 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165799 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 165834 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 165835 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165846 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165846 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165847 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165848 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 165884 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 165885 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165896 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165897 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165898 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165898 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 165935 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 165935 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165946 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165947 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165948 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165948 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 165985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 165985 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165997 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165998 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 165999 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 165999 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 166034 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 166035 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166047 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166047 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166048 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166049 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 166084 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 166085 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166097 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166097 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166098 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166099 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 166134 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 166135 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166146 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166146 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166147 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166148 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 166184 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 166185 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166196 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166196 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166198 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166199 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 166234 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 84, timestamp: 166285 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 83, timestamp: 166285 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166298 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166299 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166300 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166300 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 82, timestamp: 166318 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 81, timestamp: 166318 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 183, cameraPosition: {…}, fps: 60, frameCount: 600 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166330 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166331 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166333 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166333 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 80, timestamp: 166368 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 79, timestamp: 166368 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166391 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166391 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166392 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166392 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 78, timestamp: 166418 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 77, timestamp: 166418 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166431 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166431 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166433 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166433 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 76, timestamp: 166468 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 75, timestamp: 166469 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166483 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166483 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166484 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166485 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 74, timestamp: 166518 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 73, timestamp: 166519 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166529 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166530 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166531 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166531 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 72, timestamp: 166568 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 71, timestamp: 166568 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166590 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166590 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166591 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166591 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 70, timestamp: 166618 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 69, timestamp: 166619 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166630 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166631 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166632 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166632 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 68, timestamp: 166667 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 67, timestamp: 166668 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166679 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166681 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166682 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166683 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 66, timestamp: 166717 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 65, timestamp: 166718 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166730 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166730 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166732 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166732 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 64, timestamp: 166768 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 63, timestamp: 166769 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166790 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166790 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166791 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166791 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 62, timestamp: 166818 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 61, timestamp: 166818 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166830 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166830 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166831 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166831 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 60, timestamp: 166867 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 59, timestamp: 166868 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166879 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166880 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166881 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166881 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 58, timestamp: 166918 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 57, timestamp: 166919 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166937 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166937 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166938 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166939 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 166968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 166968 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166981 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166981 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 166983 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 166983 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 167018 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 167018 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167039 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167039 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167040 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167040 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 167068 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 167069 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167080 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167080 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167081 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167082 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 167118 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 56, timestamp: 167119 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 55, timestamp: 167119 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167131 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167132 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167133 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167133 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 54, timestamp: 167168 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 53, timestamp: 167168 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167180 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167181 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167182 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167182 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 52, timestamp: 167218 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 51, timestamp: 167219 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167230 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167230 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167232 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167232 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 50, timestamp: 167268 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 49, timestamp: 167268 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167289 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167290 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167291 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167291 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 48, timestamp: 167318 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 47, timestamp: 167319 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167339 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167339 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167340 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167340 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 46, timestamp: 167369 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 45, timestamp: 167369 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167380 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167381 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167382 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167382 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 44, timestamp: 167418 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 43, timestamp: 167419 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167431 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167432 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167433 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167434 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 42, timestamp: 167468 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 41, timestamp: 167468 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167480 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167481 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167482 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167482 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 40, timestamp: 167518 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 39, timestamp: 167519 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167531 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167531 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167532 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167532 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 167568 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 38, timestamp: 167568 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 37, timestamp: 167569 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167581 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167581 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167582 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167583 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 36, timestamp: 167618 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 35, timestamp: 167619 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167631 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167631 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167632 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167632 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 34, timestamp: 167668 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 33, timestamp: 167669 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167684 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167684 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167686 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167686 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 32, timestamp: 167718 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 167719 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167730 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167730 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167732 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167732 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 167767 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 167768 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167780 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167781 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167782 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167782 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 167818 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 167819 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167830 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167830 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167831 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167831 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 167868 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 167868 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167881 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167881 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167882 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167883 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 167918 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 167919 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167933 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167933 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167934 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167935 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 167968 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 167969 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167983 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167984 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 167985 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 167985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 168018 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 168018 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168031 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168032 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168033 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168033 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 168068 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 168069 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168080 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168081 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168082 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168083 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 168118 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 168119 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168132 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168132 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168133 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168134 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 168168 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 168169 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168183 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168184 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168190 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168190 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 168218 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 168218 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168230 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168230 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168232 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168232 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 168268 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 31, timestamp: 168334 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 30, timestamp: 168335 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168352 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168352 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168355 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168356 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 29, timestamp: 168384 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 28, timestamp: 168384 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 192, cameraPosition: {…}, fps: 60, frameCount: 720 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168407 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168407 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168408 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168408 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 27, timestamp: 168434 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 26, timestamp: 168435 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168446 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168447 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168449 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168449 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 25, timestamp: 168485 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 24, timestamp: 168486 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168508 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168509 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168511 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168511 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 23, timestamp: 168534 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 22, timestamp: 168535 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168548 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168548 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168550 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168550 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 21, timestamp: 168585 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 168585 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168598 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168598 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168599 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168600 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 168634 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 168635 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168648 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168649 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168650 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168651 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 168685 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 168685 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168697 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168698 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168700 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168700 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 168734 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 168735 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168748 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168748 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168749 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168749 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 168785 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 168785 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168797 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168797 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168805 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168806 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 168835 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 168836 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168852 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168852 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168855 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168855 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 168885 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 168885 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168896 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168897 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168898 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168898 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 168935 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 168935 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168946 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168947 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168949 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168949 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 168984 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 168985 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168996 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168997 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 168998 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 168998 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 169035 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 169035 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 169047 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 169047 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 169048 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 169048 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 169085 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 169085 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 169096 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 169097 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 169098 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 169098 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 169334 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 169359 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 169372 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 169373 }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 169634 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 170284 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 196, cameraPosition: {…}, fps: 60, frameCount: 840 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 170484 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 170534 }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Mouvement bloqué par obstacle 
Object { blockType: 0, isTree: undefined, reason: "Bloc non-grimpable" }
Logger.js:102:21
[DEBUG] Statistiques de performance 
Object { fps: 20, chunksGenerated: 516, chunksRendered: 193, chunksInQueue: 0, chunksBeingGenerated: 0 }
Logger.js:102:21
[INFO] Début du minage (clic gauche) 
Object { button: 0, pointerLocked: true }
Logger.js:102:21
🔨 Début du minage du bloc 3 à (61, 65, -48) Player.js:924:17
[DEBUG] État de la scène 
Object { sceneObjects: 196, cameraPosition: {…}, fps: 60, frameCount: 960 }
Logger.js:102:21
🔨 Bloc modifié dans chunk (3, -3) à position locale (13, 65, 0): 3 → 0 Chunk.js:583:17
🔄 Début de régénération du mesh du chunk (3, -3) Chunk.js:710:17
🔄 Mesh du chunk (3, -3) régénéré avec succès Chunk.js:777:17
🔨 Bloc modifié à (61, 65, -48): détruit World.js:741:17
📦 Ajouté à l'inventaire: 1x Herbe Player.js:1051:17
✅ Bloc miné avec succès: type 3 à (61, 65, -48) Player.js:1033:17
🔨 Arrêt du minage Player.js:944:17
[INFO] Arrêt du minage (relâchement clic) 
Object { button: 0, pointerLocked: true }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 196, cameraPosition: {…}, fps: 60, frameCount: 1080 }
Logger.js:102:21

[875] [2025-07-22T18:13:02.431Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 7048
}
---
[876] [2025-07-22T18:13:02.442Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 7059
}
---
[877] [2025-07-22T18:13:02.442Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7059
}
---
[878] [2025-07-22T18:13:02.443Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 7060
}
---
[879] [2025-07-22T18:13:02.445Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7061
}
---
[880] [2025-07-22T18:13:02.480Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 11,
  "timestamp": 7097
}
---
[881] [2025-07-22T18:13:02.482Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 10,
  "timestamp": 7097
}
---
[882] [2025-07-22T18:13:02.492Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 7109
}
---
[883] [2025-07-22T18:13:02.492Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7109
}
---
[884] [2025-07-22T18:13:02.494Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 7110
}
---
[885] [2025-07-22T18:13:02.494Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7111
}
---
[886] [2025-07-22T18:13:02.531Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 9,
  "timestamp": 7147
}
---
[887] [2025-07-22T18:13:02.531Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 8,
  "timestamp": 7148
}
---
[888] [2025-07-22T18:13:02.543Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 7161
}
---
[889] [2025-07-22T18:13:02.544Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7161
}
---
[890] [2025-07-22T18:13:02.545Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 7162
}
---
[891] [2025-07-22T18:13:02.545Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7162
}
---
[892] [2025-07-22T18:13:02.581Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 7,
  "timestamp": 7197
}
---
[893] [2025-07-22T18:13:02.581Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 6,
  "timestamp": 7198
}
---
[894] [2025-07-22T18:13:02.591Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 7208
}
---
[895] [2025-07-22T18:13:02.592Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7208
}
---
[896] [2025-07-22T18:13:02.594Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 7209
}
---
[897] [2025-07-22T18:13:02.594Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7210
}
---
[898] [2025-07-22T18:13:02.630Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 5,
  "timestamp": 7247
}
---
[899] [2025-07-22T18:13:02.630Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 4,
  "timestamp": 7247
}
---
[900] [2025-07-22T18:13:02.643Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 7260
}
---
[901] [2025-07-22T18:13:02.643Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7260
}
---
[902] [2025-07-22T18:13:02.644Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 7262
}
---
[903] [2025-07-22T18:13:02.644Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7262
}
---
[904] [2025-07-22T18:13:02.680Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 3,
  "timestamp": 7297
}
---
[905] [2025-07-22T18:13:02.681Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 2,
  "timestamp": 7297
}
---
[906] [2025-07-22T18:13:02.692Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 7309
}
---
[907] [2025-07-22T18:13:02.692Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7309
}
---
[908] [2025-07-22T18:13:02.693Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 7310
}
---
[909] [2025-07-22T18:13:02.694Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7311
}
---
[910] [2025-07-22T18:13:02.731Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 1,
  "timestamp": 7347
}
---
[911] [2025-07-22T18:13:02.731Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 0,
  "timestamp": 7348
}
---
[912] [2025-07-22T18:13:02.742Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 7360
}
---
[913] [2025-07-22T18:13:02.743Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7360
}
---
[914] [2025-07-22T18:13:02.744Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 7361
}
---
[915] [2025-07-22T18:13:02.744Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 7361
}
---
[916] [2025-07-22T18:13:03.730Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 1,
    "z": -2
  },
  "oldChunk": {
    "x": 0,
    "z": -2
  },
  "timestamp": 8347
}
---
[917] [2025-07-22T18:13:03.796Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 99,
  "timestamp": 8413
}
---
[918] [2025-07-22T18:13:03.797Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 98,
  "timestamp": 8413
}
---
[919] [2025-07-22T18:13:03.815Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 8431
}
---
[920] [2025-07-22T18:13:03.815Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8432
}
---
[921] [2025-07-22T18:13:03.817Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 8433
}
---
[922] [2025-07-22T18:13:03.817Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8434
}
---
[923] [2025-07-22T18:13:03.847Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 97,
  "timestamp": 8464
}
---
[924] [2025-07-22T18:13:03.848Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 96,
  "timestamp": 8464
}
---
[925] [2025-07-22T18:13:03.859Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 8476
}
---
[926] [2025-07-22T18:13:03.859Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8476
}
---
[927] [2025-07-22T18:13:03.860Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 8477
}
---
[928] [2025-07-22T18:13:03.861Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8477
}
---
[929] [2025-07-22T18:13:03.897Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 95,
  "timestamp": 8513
}
---
[930] [2025-07-22T18:13:03.898Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 94,
  "timestamp": 8514
}
---
[931] [2025-07-22T18:13:03.909Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 8525
}
---
[932] [2025-07-22T18:13:03.910Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 0
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8526
}
---
[933] [2025-07-22T18:13:03.910Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 8527
}
---
[934] [2025-07-22T18:13:03.911Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8527
}
---
[935] [2025-07-22T18:13:03.947Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 93,
  "timestamp": 8563
}
---
[936] [2025-07-22T18:13:03.947Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 92,
  "timestamp": 8564
}
---
[937] [2025-07-22T18:13:03.958Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 8575
}
---
[938] [2025-07-22T18:13:03.959Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8575
}
---
[939] [2025-07-22T18:13:03.960Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 8576
}
---
[940] [2025-07-22T18:13:03.960Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8577
}
---
[941] [2025-07-22T18:13:03.997Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 1,
    "z": -3
  },
  "oldChunk": {
    "x": 1,
    "z": -2
  },
  "timestamp": 8613
}
---
[942] [2025-07-22T18:13:04.051Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 99,
  "timestamp": 8668
}
---
[943] [2025-07-22T18:13:04.051Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 98,
  "timestamp": 8668
}
---
[944] [2025-07-22T18:13:04.064Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 8681
}
---
[945] [2025-07-22T18:13:04.064Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8681
}
---
[946] [2025-07-22T18:13:04.067Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 8684
}
---
[947] [2025-07-22T18:13:04.067Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8684
}
---
[948] [2025-07-22T18:13:04.098Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 97,
  "timestamp": 8714
}
---
[949] [2025-07-22T18:13:04.098Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 96,
  "timestamp": 8714
}
---
[950] [2025-07-22T18:13:04.112Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 8729
}
---
[951] [2025-07-22T18:13:04.114Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8730
}
---
[952] [2025-07-22T18:13:04.117Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 8734
}
---
[953] [2025-07-22T18:13:04.117Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8734
}
---
[954] [2025-07-22T18:13:04.147Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 95,
  "timestamp": 8764
}
---
[955] [2025-07-22T18:13:04.147Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 94,
  "timestamp": 8765
}
---
[956] [2025-07-22T18:13:04.158Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 8775
}
---
[957] [2025-07-22T18:13:04.159Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8775
}
---
[958] [2025-07-22T18:13:04.160Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 8776
}
---
[959] [2025-07-22T18:13:04.160Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8776
}
---
[960] [2025-07-22T18:13:04.196Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 93,
  "timestamp": 8814
}
---
[961] [2025-07-22T18:13:04.197Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 92,
  "timestamp": 8814
}
---
[962] [2025-07-22T18:13:04.209Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 8825
}
---
[963] [2025-07-22T18:13:04.209Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8826
}
---
[964] [2025-07-22T18:13:04.211Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 8827
}
---
[965] [2025-07-22T18:13:04.211Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8828
}
---
[966] [2025-07-22T18:13:04.247Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 91,
  "timestamp": 8863
}
---
[967] [2025-07-22T18:13:04.248Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 90,
  "timestamp": 8863
}
---
[968] [2025-07-22T18:13:04.259Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 8876
}
---
[969] [2025-07-22T18:13:04.259Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8876
}
---
[970] [2025-07-22T18:13:04.260Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 8877
}
---
[971] [2025-07-22T18:13:04.260Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8877
}
---
[972] [2025-07-22T18:13:04.297Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 89,
  "timestamp": 8913
}
---
[973] [2025-07-22T18:13:04.297Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 88,
  "timestamp": 8914
}
---
[974] [2025-07-22T18:13:04.319Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 8935
}
---
[975] [2025-07-22T18:13:04.319Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8935
}
---
[976] [2025-07-22T18:13:04.320Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 8936
}
---
[977] [2025-07-22T18:13:04.320Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8936
}
---
[978] [2025-07-22T18:13:04.346Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 87,
  "timestamp": 8963
}
---
[979] [2025-07-22T18:13:04.348Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 86,
  "timestamp": 8964
}
---
[980] [2025-07-22T18:13:04.360Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 8976
}
---
[981] [2025-07-22T18:13:04.360Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8977
}
---
[982] [2025-07-22T18:13:04.360Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 8978
}
---
[983] [2025-07-22T18:13:04.361Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 8978
}
---
[984] [2025-07-22T18:13:04.397Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 85,
  "timestamp": 9013
}
---
[985] [2025-07-22T18:13:04.397Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 84,
  "timestamp": 9014
}
---
[986] [2025-07-22T18:13:04.397Z] [DEBUG] État de la scène
{
  "sceneObjects": 183,
  "cameraPosition": {
    "x": 21.35,
    "y": 72.68,
    "z": -34.75
  },
  "fps": 60,
  "frameCount": 480
}
---
[987] [2025-07-22T18:13:04.409Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 9025
}
---
[988] [2025-07-22T18:13:04.409Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9025
}
---
[989] [2025-07-22T18:13:04.409Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 9026
}
---
[990] [2025-07-22T18:13:04.410Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9026
}
---
[991] [2025-07-22T18:13:04.447Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 83,
  "timestamp": 9064
}
---
[992] [2025-07-22T18:13:04.447Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 82,
  "timestamp": 9064
}
---
[993] [2025-07-22T18:13:04.460Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 9076
}
---
[994] [2025-07-22T18:13:04.460Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9077
}
---
[995] [2025-07-22T18:13:04.461Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 9077
}
---
[996] [2025-07-22T18:13:04.461Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9077
}
---
[997] [2025-07-22T18:13:04.497Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 81,
  "timestamp": 9114
}
---
[998] [2025-07-22T18:13:04.497Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 80,
  "timestamp": 9114
}
---
[999] [2025-07-22T18:13:04.519Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 9136
}
---
[1000] [2025-07-22T18:13:04.519Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9136
}
---
[1001] [2025-07-22T18:13:04.520Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 9137
}
---
[1002] [2025-07-22T18:13:04.520Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9137
}
---
[1003] [2025-07-22T18:13:04.547Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 79,
  "timestamp": 9164
}
---
[1004] [2025-07-22T18:13:04.547Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 78,
  "timestamp": 9164
}
---
[1005] [2025-07-22T18:13:04.558Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 9174
}
---
[1006] [2025-07-22T18:13:04.558Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9175
}
---
[1007] [2025-07-22T18:13:04.559Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 9177
}
---
[1008] [2025-07-22T18:13:04.559Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9177
}
---
[1009] [2025-07-22T18:13:04.597Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 77,
  "timestamp": 9214
}
---
[1010] [2025-07-22T18:13:04.598Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 76,
  "timestamp": 9214
}
---
[1011] [2025-07-22T18:13:04.608Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 9225
}
---
[1012] [2025-07-22T18:13:04.609Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9226
}
---
[1013] [2025-07-22T18:13:04.610Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 9227
}
---
[1014] [2025-07-22T18:13:04.610Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9227
}
---
[1015] [2025-07-22T18:13:04.647Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 75,
  "timestamp": 9263
}
---
[1016] [2025-07-22T18:13:04.648Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 74,
  "timestamp": 9264
}
---
[1017] [2025-07-22T18:13:04.659Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 9277
}
---
[1018] [2025-07-22T18:13:04.660Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9277
}
---
[1019] [2025-07-22T18:13:04.661Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 9278
}
---
[1020] [2025-07-22T18:13:04.662Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 0
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9278
}
---
[1021] [2025-07-22T18:13:04.697Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 73,
  "timestamp": 9314
}
---
[1022] [2025-07-22T18:13:04.698Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 72,
  "timestamp": 9314
}
---
[1023] [2025-07-22T18:13:04.708Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 9325
}
---
[1024] [2025-07-22T18:13:04.708Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9326
}
---
[1025] [2025-07-22T18:13:04.709Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 9326
}
---
[1026] [2025-07-22T18:13:04.709Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9326
}
---
[1027] [2025-07-22T18:13:04.747Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 71,
  "timestamp": 9363
}
---
[1028] [2025-07-22T18:13:04.748Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 70,
  "timestamp": 9364
}
---
[1029] [2025-07-22T18:13:04.759Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 9375
}
---
[1030] [2025-07-22T18:13:04.759Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9376
}
---
[1031] [2025-07-22T18:13:04.760Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 9377
}
---
[1032] [2025-07-22T18:13:04.761Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9377
}
---
[1033] [2025-07-22T18:13:04.797Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 69,
  "timestamp": 9413
}
---
[1034] [2025-07-22T18:13:04.797Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 68,
  "timestamp": 9414
}
---
[1035] [2025-07-22T18:13:04.813Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 9429
}
---
[1036] [2025-07-22T18:13:04.813Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9429
}
---
[1037] [2025-07-22T18:13:04.817Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 9433
}
---
[1038] [2025-07-22T18:13:04.817Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9433
}
---
[1039] [2025-07-22T18:13:04.847Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 67,
  "timestamp": 9463
}
---
[1040] [2025-07-22T18:13:04.848Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 66,
  "timestamp": 9464
}
---
[1041] [2025-07-22T18:13:04.867Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 9484
}
---
[1042] [2025-07-22T18:13:04.867Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9484
}
---
[1043] [2025-07-22T18:13:04.868Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 9486
}
---
[1044] [2025-07-22T18:13:04.869Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9486
}
---
[1045] [2025-07-22T18:13:04.897Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 65,
  "timestamp": 9513
}
---
[1046] [2025-07-22T18:13:04.898Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 64,
  "timestamp": 9514
}
---
[1047] [2025-07-22T18:13:04.910Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 9526
}
---
[1048] [2025-07-22T18:13:04.910Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9527
}
---
[1049] [2025-07-22T18:13:04.912Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 9528
}
---
[1050] [2025-07-22T18:13:04.912Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9528
}
---
[1051] [2025-07-22T18:13:04.947Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 63,
  "timestamp": 9563
}
---
[1052] [2025-07-22T18:13:04.947Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 62,
  "timestamp": 9564
}
---
[1053] [2025-07-22T18:13:04.960Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 9577
}
---
[1054] [2025-07-22T18:13:04.961Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9577
}
---
[1055] [2025-07-22T18:13:04.964Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 9581
}
---
[1056] [2025-07-22T18:13:04.964Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9581
}
---
[1057] [2025-07-22T18:13:04.997Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 61,
  "timestamp": 9614
}
---
[1058] [2025-07-22T18:13:04.999Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 60,
  "timestamp": 9615
}
---
[1059] [2025-07-22T18:13:05.012Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 9628
}
---
[1060] [2025-07-22T18:13:05.012Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9628
}
---
[1061] [2025-07-22T18:13:05.013Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 9630
}
---
[1062] [2025-07-22T18:13:05.013Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9630
}
---
[1063] [2025-07-22T18:13:05.047Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 59,
  "timestamp": 9663
}
---
[1064] [2025-07-22T18:13:05.048Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 58,
  "timestamp": 9664
}
---
[1065] [2025-07-22T18:13:05.060Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 9677
}
---
[1066] [2025-07-22T18:13:05.061Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9677
}
---
[1067] [2025-07-22T18:13:05.062Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 9678
}
---
[1068] [2025-07-22T18:13:05.062Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9679
}
---
[1069] [2025-07-22T18:13:05.097Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 57,
  "timestamp": 9714
}
---
[1070] [2025-07-22T18:13:05.097Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 56,
  "timestamp": 9714
}
---
[1071] [2025-07-22T18:13:05.117Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 9733
}
---
[1072] [2025-07-22T18:13:05.117Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9734
}
---
[1073] [2025-07-22T18:13:05.118Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 9735
}
---
[1074] [2025-07-22T18:13:05.118Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9735
}
---
[1075] [2025-07-22T18:13:05.147Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 55,
  "timestamp": 9763
}
---
[1076] [2025-07-22T18:13:05.148Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 54,
  "timestamp": 9764
}
---
[1077] [2025-07-22T18:13:05.161Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 9777
}
---
[1078] [2025-07-22T18:13:05.161Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9778
}
---
[1079] [2025-07-22T18:13:05.167Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 9784
}
---
[1080] [2025-07-22T18:13:05.167Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9784
}
---
[1081] [2025-07-22T18:13:05.196Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 53,
  "timestamp": 9814
}
---
[1082] [2025-07-22T18:13:05.198Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 52,
  "timestamp": 9814
}
---
[1083] [2025-07-22T18:13:05.209Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 9826
}
---
[1084] [2025-07-22T18:13:05.210Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9826
}
---
[1085] [2025-07-22T18:13:05.210Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 9827
}
---
[1086] [2025-07-22T18:13:05.210Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9827
}
---
[1087] [2025-07-22T18:13:05.246Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 51,
  "timestamp": 9864
}
---
[1088] [2025-07-22T18:13:05.247Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 50,
  "timestamp": 9864
}
---
[1089] [2025-07-22T18:13:05.260Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 9875
}
---
[1090] [2025-07-22T18:13:05.260Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9876
}
---
[1091] [2025-07-22T18:13:05.261Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 9877
}
---
[1092] [2025-07-22T18:13:05.261Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9877
}
---
[1093] [2025-07-22T18:13:05.297Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 49,
  "timestamp": 9914
}
---
[1094] [2025-07-22T18:13:05.298Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 48,
  "timestamp": 9914
}
---
[1095] [2025-07-22T18:13:05.310Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 9925
}
---
[1096] [2025-07-22T18:13:05.310Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9927
}
---
[1097] [2025-07-22T18:13:05.312Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 9929
}
---
[1098] [2025-07-22T18:13:05.312Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9929
}
---
[1099] [2025-07-22T18:13:05.347Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 47,
  "timestamp": 9964
}
---
[1100] [2025-07-22T18:13:05.347Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 46,
  "timestamp": 9964
}
---
[1101] [2025-07-22T18:13:05.359Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 9976
}
---
[1102] [2025-07-22T18:13:05.359Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9976
}
---
[1103] [2025-07-22T18:13:05.360Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 9977
}
---
[1104] [2025-07-22T18:13:05.361Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 9977
}
---
[1105] [2025-07-22T18:13:05.397Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 45,
  "timestamp": 10013
}
---
[1106] [2025-07-22T18:13:05.397Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 44,
  "timestamp": 10014
}
---
[1107] [2025-07-22T18:13:05.409Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10027
}
---
[1108] [2025-07-22T18:13:05.411Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10027
}
---
[1109] [2025-07-22T18:13:05.411Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10028
}
---
[1110] [2025-07-22T18:13:05.411Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10028
}
---
[1111] [2025-07-22T18:13:05.447Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 43,
  "timestamp": 10064
}
---
[1112] [2025-07-22T18:13:05.447Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 42,
  "timestamp": 10064
}
---
[1113] [2025-07-22T18:13:05.458Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10075
}
---
[1114] [2025-07-22T18:13:05.458Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10075
}
---
[1115] [2025-07-22T18:13:05.459Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10076
}
---
[1116] [2025-07-22T18:13:05.460Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10076
}
---
[1117] [2025-07-22T18:13:05.497Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 41,
  "timestamp": 10114
}
---
[1118] [2025-07-22T18:13:05.498Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 40,
  "timestamp": 10115
}
---
[1119] [2025-07-22T18:13:05.510Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 10127
}
---
[1120] [2025-07-22T18:13:05.511Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10127
}
---
[1121] [2025-07-22T18:13:05.511Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 10129
}
---
[1122] [2025-07-22T18:13:05.511Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10129
}
---
[1123] [2025-07-22T18:13:05.547Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 39,
  "timestamp": 10164
}
---
[1124] [2025-07-22T18:13:05.548Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 38,
  "timestamp": 10164
}
---
[1125] [2025-07-22T18:13:05.560Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10176
}
---
[1126] [2025-07-22T18:13:05.563Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10179
}
---
[1127] [2025-07-22T18:13:05.564Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10181
}
---
[1128] [2025-07-22T18:13:05.565Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10181
}
---
[1129] [2025-07-22T18:13:05.597Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 37,
  "timestamp": 10214
}
---
[1130] [2025-07-22T18:13:05.599Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 36,
  "timestamp": 10215
}
---
[1131] [2025-07-22T18:13:05.609Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10226
}
---
[1132] [2025-07-22T18:13:05.610Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10226
}
---
[1133] [2025-07-22T18:13:05.618Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10234
}
---
[1134] [2025-07-22T18:13:05.618Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10235
}
---
[1135] [2025-07-22T18:13:05.647Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 35,
  "timestamp": 10264
}
---
[1136] [2025-07-22T18:13:05.648Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 34,
  "timestamp": 10264
}
---
[1137] [2025-07-22T18:13:05.658Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 10275
}
---
[1138] [2025-07-22T18:13:05.658Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10275
}
---
[1139] [2025-07-22T18:13:05.660Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 10276
}
---
[1140] [2025-07-22T18:13:05.660Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": 0
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10276
}
---
[1141] [2025-07-22T18:13:05.698Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 33,
  "timestamp": 10314
}
---
[1142] [2025-07-22T18:13:05.698Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 32,
  "timestamp": 10315
}
---
[1143] [2025-07-22T18:13:05.714Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 10331
}
---
[1144] [2025-07-22T18:13:05.715Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10331
}
---
[1145] [2025-07-22T18:13:05.718Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 10334
}
---
[1146] [2025-07-22T18:13:05.718Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10334
}
---
[1147] [2025-07-22T18:13:05.747Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 31,
  "timestamp": 10364
}
---
[1148] [2025-07-22T18:13:05.747Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 30,
  "timestamp": 10364
}
---
[1149] [2025-07-22T18:13:05.760Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 10377
}
---
[1150] [2025-07-22T18:13:05.760Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10377
}
---
[1151] [2025-07-22T18:13:05.761Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 10377
}
---
[1152] [2025-07-22T18:13:05.762Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10378
}
---
[1153] [2025-07-22T18:13:05.797Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 29,
  "timestamp": 10414
}
---
[1154] [2025-07-22T18:13:05.798Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": 7
  },
  "priority": 2,
  "queueRemaining": 28,
  "timestamp": 10414
}
---
[1155] [2025-07-22T18:13:05.817Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 10434
}
---
[1156] [2025-07-22T18:13:05.817Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10435
}
---
[1157] [2025-07-22T18:13:05.819Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": 7
  },
  "blocksReceived": 32768,
  "timestamp": 10435
}
---
[1158] [2025-07-22T18:13:05.820Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": 7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10435
}
---
[1159] [2025-07-22T18:13:05.847Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 27,
  "timestamp": 10463
}
---
[1160] [2025-07-22T18:13:05.847Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 26,
  "timestamp": 10464
}
---
[1161] [2025-07-22T18:13:05.858Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 10474
}
---
[1162] [2025-07-22T18:13:05.858Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10475
}
---
[1163] [2025-07-22T18:13:05.860Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 10476
}
---
[1164] [2025-07-22T18:13:05.860Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10476
}
---
[1165] [2025-07-22T18:13:05.897Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 25,
  "timestamp": 10514
}
---
[1166] [2025-07-22T18:13:05.898Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 24,
  "timestamp": 10514
}
---
[1167] [2025-07-22T18:13:05.910Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10526
}
---
[1168] [2025-07-22T18:13:05.910Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10527
}
---
[1169] [2025-07-22T18:13:05.912Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10528
}
---
[1170] [2025-07-22T18:13:05.912Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10529
}
---
[1171] [2025-07-22T18:13:05.947Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 23,
  "timestamp": 10563
}
---
[1172] [2025-07-22T18:13:05.948Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 22,
  "timestamp": 10564
}
---
[1173] [2025-07-22T18:13:05.959Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10576
}
---
[1174] [2025-07-22T18:13:05.959Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10576
}
---
[1175] [2025-07-22T18:13:05.960Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10577
}
---
[1176] [2025-07-22T18:13:05.961Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10577
}
---
[1177] [2025-07-22T18:13:05.997Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 21,
  "timestamp": 10613
}
---
[1178] [2025-07-22T18:13:05.998Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 10614
}
---
[1179] [2025-07-22T18:13:06.009Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 10626
}
---
[1180] [2025-07-22T18:13:06.009Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10626
}
---
[1181] [2025-07-22T18:13:06.012Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 10628
}
---
[1182] [2025-07-22T18:13:06.012Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10628
}
---
[1183] [2025-07-22T18:13:06.046Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 10664
}
---
[1184] [2025-07-22T18:13:06.047Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 10664
}
---
[1185] [2025-07-22T18:13:06.062Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 10679
}
---
[1186] [2025-07-22T18:13:06.062Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10679
}
---
[1187] [2025-07-22T18:13:06.063Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 10679
}
---
[1188] [2025-07-22T18:13:06.063Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10681
}
---
[1189] [2025-07-22T18:13:06.096Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 1,
    "z": -4
  },
  "oldChunk": {
    "x": 1,
    "z": -3
  },
  "timestamp": 10713
}
---
[1190] [2025-07-22T18:13:06.162Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 99,
  "timestamp": 10779
}
---
[1191] [2025-07-22T18:13:06.162Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 98,
  "timestamp": 10779
}
---
[1192] [2025-07-22T18:13:06.179Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 10796
}
---
[1193] [2025-07-22T18:13:06.179Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10796
}
---
[1194] [2025-07-22T18:13:06.180Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 10797
}
---
[1195] [2025-07-22T18:13:06.180Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10797
}
---
[1196] [2025-07-22T18:13:06.213Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 97,
  "timestamp": 10831
}
---
[1197] [2025-07-22T18:13:06.215Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 96,
  "timestamp": 10831
}
---
[1198] [2025-07-22T18:13:06.227Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 10843
}
---
[1199] [2025-07-22T18:13:06.227Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10843
}
---
[1200] [2025-07-22T18:13:06.228Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 10845
}
---
[1201] [2025-07-22T18:13:06.228Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10845
}
---
[1202] [2025-07-22T18:13:06.264Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 95,
  "timestamp": 10881
}
---
[1203] [2025-07-22T18:13:06.265Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 94,
  "timestamp": 10881
}
---
[1204] [2025-07-22T18:13:06.275Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 10893
}
---
[1205] [2025-07-22T18:13:06.276Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10893
}
---
[1206] [2025-07-22T18:13:06.277Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 10894
}
---
[1207] [2025-07-22T18:13:06.277Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10895
}
---
[1208] [2025-07-22T18:13:06.313Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 93,
  "timestamp": 10931
}
---
[1209] [2025-07-22T18:13:06.314Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 92,
  "timestamp": 10931
}
---
[1210] [2025-07-22T18:13:06.326Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 10943
}
---
[1211] [2025-07-22T18:13:06.328Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10944
}
---
[1212] [2025-07-22T18:13:06.329Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 10945
}
---
[1213] [2025-07-22T18:13:06.329Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10945
}
---
[1214] [2025-07-22T18:13:06.363Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 91,
  "timestamp": 10980
}
---
[1215] [2025-07-22T18:13:06.363Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": 8
  },
  "priority": 2,
  "queueRemaining": 90,
  "timestamp": 10980
}
---
[1216] [2025-07-22T18:13:06.377Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": 8
  },
  "blocksReceived": 32768,
  "timestamp": 10993
}
---
[1217] [2025-07-22T18:13:06.377Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": 8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10993
}
---
[1218] [2025-07-22T18:13:06.377Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 10994
}
---
[1219] [2025-07-22T18:13:06.377Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 10995
}
---
[1220] [2025-07-22T18:13:06.414Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 89,
  "timestamp": 11031
}
---
[1221] [2025-07-22T18:13:06.415Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 88,
  "timestamp": 11031
}
---
[1222] [2025-07-22T18:13:06.426Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11043
}
---
[1223] [2025-07-22T18:13:06.426Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11043
}
---
[1224] [2025-07-22T18:13:06.428Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 11044
}
---
[1225] [2025-07-22T18:13:06.428Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11044
}
---
[1226] [2025-07-22T18:13:06.464Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 87,
  "timestamp": 11081
}
---
[1227] [2025-07-22T18:13:06.465Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 86,
  "timestamp": 11081
}
---
[1228] [2025-07-22T18:13:06.465Z] [DEBUG] État de la scène
{
  "sceneObjects": 194,
  "cameraPosition": {
    "x": 19.31,
    "y": 71.62,
    "z": -50.74
  },
  "fps": 60,
  "frameCount": 600
}
---
[1229] [2025-07-22T18:13:06.478Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 11094
}
---
[1230] [2025-07-22T18:13:06.478Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11095
}
---
[1231] [2025-07-22T18:13:06.478Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11096
}
---
[1232] [2025-07-22T18:13:06.479Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11096
}
---
[1233] [2025-07-22T18:13:06.514Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 85,
  "timestamp": 11130
}
---
[1234] [2025-07-22T18:13:06.514Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 84,
  "timestamp": 11130
}
---
[1235] [2025-07-22T18:13:06.526Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11142
}
---
[1236] [2025-07-22T18:13:06.526Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11142
}
---
[1237] [2025-07-22T18:13:06.527Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11144
}
---
[1238] [2025-07-22T18:13:06.527Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11144
}
---
[1239] [2025-07-22T18:13:06.564Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 83,
  "timestamp": 11180
}
---
[1240] [2025-07-22T18:13:06.565Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 82,
  "timestamp": 11181
}
---
[1241] [2025-07-22T18:13:06.576Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11193
}
---
[1242] [2025-07-22T18:13:06.578Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11193
}
---
[1243] [2025-07-22T18:13:06.578Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11194
}
---
[1244] [2025-07-22T18:13:06.579Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11195
}
---
[1245] [2025-07-22T18:13:06.614Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 81,
  "timestamp": 11230
}
---
[1246] [2025-07-22T18:13:06.614Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 80,
  "timestamp": 11230
}
---
[1247] [2025-07-22T18:13:06.626Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11242
}
---
[1248] [2025-07-22T18:13:06.627Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11243
}
---
[1249] [2025-07-22T18:13:06.628Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11244
}
---
[1250] [2025-07-22T18:13:06.628Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11245
}
---
[1251] [2025-07-22T18:13:06.664Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 79,
  "timestamp": 11281
}
---
[1252] [2025-07-22T18:13:06.665Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 78,
  "timestamp": 11281
}
---
[1253] [2025-07-22T18:13:06.677Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11293
}
---
[1254] [2025-07-22T18:13:06.677Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11294
}
---
[1255] [2025-07-22T18:13:06.678Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11295
}
---
[1256] [2025-07-22T18:13:06.679Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11295
}
---
[1257] [2025-07-22T18:13:06.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 77,
  "timestamp": 11331
}
---
[1258] [2025-07-22T18:13:06.715Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 76,
  "timestamp": 11331
}
---
[1259] [2025-07-22T18:13:06.727Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11344
}
---
[1260] [2025-07-22T18:13:06.727Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11344
}
---
[1261] [2025-07-22T18:13:06.728Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 11345
}
---
[1262] [2025-07-22T18:13:06.729Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11345
}
---
[1263] [2025-07-22T18:13:06.747Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1264] [2025-07-22T18:13:06.764Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1265] [2025-07-22T18:13:06.765Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 75,
  "timestamp": 11382
}
---
[1266] [2025-07-22T18:13:06.765Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 74,
  "timestamp": 11382
}
---
[1267] [2025-07-22T18:13:06.776Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11393
}
---
[1268] [2025-07-22T18:13:06.777Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11393
}
---
[1269] [2025-07-22T18:13:06.778Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 11395
}
---
[1270] [2025-07-22T18:13:06.778Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11395
}
---
[1271] [2025-07-22T18:13:06.780Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1272] [2025-07-22T18:13:06.797Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1273] [2025-07-22T18:13:06.814Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1274] [2025-07-22T18:13:06.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 73,
  "timestamp": 11431
}
---
[1275] [2025-07-22T18:13:06.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 72,
  "timestamp": 11431
}
---
[1276] [2025-07-22T18:13:06.830Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1277] [2025-07-22T18:13:06.835Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 11452
}
---
[1278] [2025-07-22T18:13:06.835Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11452
}
---
[1279] [2025-07-22T18:13:06.837Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 11453
}
---
[1280] [2025-07-22T18:13:06.837Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11454
}
---
[1281] [2025-07-22T18:13:06.847Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1282] [2025-07-22T18:13:06.864Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1283] [2025-07-22T18:13:06.864Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 71,
  "timestamp": 11480
}
---
[1284] [2025-07-22T18:13:06.865Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 70,
  "timestamp": 11481
}
---
[1285] [2025-07-22T18:13:06.877Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11494
}
---
[1286] [2025-07-22T18:13:06.877Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11494
}
---
[1287] [2025-07-22T18:13:06.879Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 11495
}
---
[1288] [2025-07-22T18:13:06.879Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11496
}
---
[1289] [2025-07-22T18:13:06.880Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1290] [2025-07-22T18:13:06.914Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 69,
  "timestamp": 11530
}
---
[1291] [2025-07-22T18:13:06.914Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 68,
  "timestamp": 11531
}
---
[1292] [2025-07-22T18:13:06.927Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11543
}
---
[1293] [2025-07-22T18:13:06.927Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11543
}
---
[1294] [2025-07-22T18:13:06.928Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11545
}
---
[1295] [2025-07-22T18:13:06.928Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11545
}
---
[1296] [2025-07-22T18:13:06.963Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 67,
  "timestamp": 11581
}
---
[1297] [2025-07-22T18:13:06.963Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 66,
  "timestamp": 11581
}
---
[1298] [2025-07-22T18:13:06.977Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11593
}
---
[1299] [2025-07-22T18:13:06.977Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11594
}
---
[1300] [2025-07-22T18:13:06.978Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 11595
}
---
[1301] [2025-07-22T18:13:06.979Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11596
}
---
[1302] [2025-07-22T18:13:07.013Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 65,
  "timestamp": 11630
}
---
[1303] [2025-07-22T18:13:07.014Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 64,
  "timestamp": 11631
}
---
[1304] [2025-07-22T18:13:07.026Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11643
}
---
[1305] [2025-07-22T18:13:07.027Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11644
}
---
[1306] [2025-07-22T18:13:07.028Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11645
}
---
[1307] [2025-07-22T18:13:07.028Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11645
}
---
[1308] [2025-07-22T18:13:07.063Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 63,
  "timestamp": 11680
}
---
[1309] [2025-07-22T18:13:07.065Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 62,
  "timestamp": 11681
}
---
[1310] [2025-07-22T18:13:07.077Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11694
}
---
[1311] [2025-07-22T18:13:07.078Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11694
}
---
[1312] [2025-07-22T18:13:07.079Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 11695
}
---
[1313] [2025-07-22T18:13:07.079Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11695
}
---
[1314] [2025-07-22T18:13:07.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 61,
  "timestamp": 11731
}
---
[1315] [2025-07-22T18:13:07.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 60,
  "timestamp": 11731
}
---
[1316] [2025-07-22T18:13:07.133Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11750
}
---
[1317] [2025-07-22T18:13:07.134Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11751
}
---
[1318] [2025-07-22T18:13:07.136Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 11752
}
---
[1319] [2025-07-22T18:13:07.136Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11752
}
---
[1320] [2025-07-22T18:13:07.164Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 59,
  "timestamp": 11780
}
---
[1321] [2025-07-22T18:13:07.164Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 58,
  "timestamp": 11781
}
---
[1322] [2025-07-22T18:13:07.177Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 11794
}
---
[1323] [2025-07-22T18:13:07.177Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11794
}
---
[1324] [2025-07-22T18:13:07.178Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 11795
}
---
[1325] [2025-07-22T18:13:07.178Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11795
}
---
[1326] [2025-07-22T18:13:07.214Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 0,
    "z": -4
  },
  "oldChunk": {
    "x": 1,
    "z": -4
  },
  "timestamp": 11830
}
---
[1327] [2025-07-22T18:13:07.238Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 83,
  "timestamp": 11855
}
---
[1328] [2025-07-22T18:13:07.238Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 82,
  "timestamp": 11855
}
---
[1329] [2025-07-22T18:13:07.253Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11869
}
---
[1330] [2025-07-22T18:13:07.254Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11870
}
---
[1331] [2025-07-22T18:13:07.255Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 11871
}
---
[1332] [2025-07-22T18:13:07.255Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11871
}
---
[1333] [2025-07-22T18:13:07.263Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 81,
  "timestamp": 11881
}
---
[1334] [2025-07-22T18:13:07.264Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 80,
  "timestamp": 11881
}
---
[1335] [2025-07-22T18:13:07.276Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 11893
}
---
[1336] [2025-07-22T18:13:07.278Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11894
}
---
[1337] [2025-07-22T18:13:07.278Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 11895
}
---
[1338] [2025-07-22T18:13:07.278Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11895
}
---
[1339] [2025-07-22T18:13:07.313Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 79,
  "timestamp": 11930
}
---
[1340] [2025-07-22T18:13:07.313Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 78,
  "timestamp": 11931
}
---
[1341] [2025-07-22T18:13:07.326Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 11944
}
---
[1342] [2025-07-22T18:13:07.327Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11944
}
---
[1343] [2025-07-22T18:13:07.328Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11945
}
---
[1344] [2025-07-22T18:13:07.328Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11945
}
---
[1345] [2025-07-22T18:13:07.363Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 77,
  "timestamp": 11980
}
---
[1346] [2025-07-22T18:13:07.364Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 76,
  "timestamp": 11981
}
---
[1347] [2025-07-22T18:13:07.376Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 11993
}
---
[1348] [2025-07-22T18:13:07.376Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11994
}
---
[1349] [2025-07-22T18:13:07.378Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 11994
}
---
[1350] [2025-07-22T18:13:07.378Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 11995
}
---
[1351] [2025-07-22T18:13:07.414Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 75,
  "timestamp": 12030
}
---
[1352] [2025-07-22T18:13:07.414Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 74,
  "timestamp": 12031
}
---
[1353] [2025-07-22T18:13:07.427Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12043
}
---
[1354] [2025-07-22T18:13:07.427Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12044
}
---
[1355] [2025-07-22T18:13:07.428Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 12045
}
---
[1356] [2025-07-22T18:13:07.428Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12045
}
---
[1357] [2025-07-22T18:13:07.464Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 73,
  "timestamp": 12080
}
---
[1358] [2025-07-22T18:13:07.465Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 72,
  "timestamp": 12082
}
---
[1359] [2025-07-22T18:13:07.475Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12093
}
---
[1360] [2025-07-22T18:13:07.476Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12093
}
---
[1361] [2025-07-22T18:13:07.477Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 12094
}
---
[1362] [2025-07-22T18:13:07.477Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12094
}
---
[1363] [2025-07-22T18:13:07.514Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 71,
  "timestamp": 12130
}
---
[1364] [2025-07-22T18:13:07.515Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 70,
  "timestamp": 12131
}
---
[1365] [2025-07-22T18:13:07.526Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12143
}
---
[1366] [2025-07-22T18:13:07.528Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12144
}
---
[1367] [2025-07-22T18:13:07.528Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 12145
}
---
[1368] [2025-07-22T18:13:07.529Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12146
}
---
[1369] [2025-07-22T18:13:07.564Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 69,
  "timestamp": 12180
}
---
[1370] [2025-07-22T18:13:07.565Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 68,
  "timestamp": 12182
}
---
[1371] [2025-07-22T18:13:07.577Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 12192
}
---
[1372] [2025-07-22T18:13:07.577Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12193
}
---
[1373] [2025-07-22T18:13:07.578Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12194
}
---
[1374] [2025-07-22T18:13:07.578Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12195
}
---
[1375] [2025-07-22T18:13:07.614Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 67,
  "timestamp": 12230
}
---
[1376] [2025-07-22T18:13:07.614Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 66,
  "timestamp": 12231
}
---
[1377] [2025-07-22T18:13:07.627Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12243
}
---
[1378] [2025-07-22T18:13:07.627Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12243
}
---
[1379] [2025-07-22T18:13:07.628Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 12245
}
---
[1380] [2025-07-22T18:13:07.629Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12245
}
---
[1381] [2025-07-22T18:13:07.664Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 65,
  "timestamp": 12280
}
---
[1382] [2025-07-22T18:13:07.664Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 64,
  "timestamp": 12280
}
---
[1383] [2025-07-22T18:13:07.686Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12303
}
---
[1384] [2025-07-22T18:13:07.686Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12303
}
---
[1385] [2025-07-22T18:13:07.689Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 12305
}
---
[1386] [2025-07-22T18:13:07.689Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12305
}
---
[1387] [2025-07-22T18:13:07.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 63,
  "timestamp": 12331
}
---
[1388] [2025-07-22T18:13:07.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 62,
  "timestamp": 12331
}
---
[1389] [2025-07-22T18:13:07.727Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 12344
}
---
[1390] [2025-07-22T18:13:07.727Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12344
}
---
[1391] [2025-07-22T18:13:07.728Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 12345
}
---
[1392] [2025-07-22T18:13:07.729Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12345
}
---
[1393] [2025-07-22T18:13:07.764Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 61,
  "timestamp": 12380
}
---
[1394] [2025-07-22T18:13:07.764Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 60,
  "timestamp": 12381
}
---
[1395] [2025-07-22T18:13:07.777Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12394
}
---
[1396] [2025-07-22T18:13:07.778Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12395
}
---
[1397] [2025-07-22T18:13:07.784Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 12400
}
---
[1398] [2025-07-22T18:13:07.784Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12401
}
---
[1399] [2025-07-22T18:13:07.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 59,
  "timestamp": 12430
}
---
[1400] [2025-07-22T18:13:07.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 58,
  "timestamp": 12430
}
---
[1401] [2025-07-22T18:13:07.825Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 12442
}
---
[1402] [2025-07-22T18:13:07.825Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12442
}
---
[1403] [2025-07-22T18:13:07.826Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12443
}
---
[1404] [2025-07-22T18:13:07.827Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12443
}
---
[1405] [2025-07-22T18:13:07.847Z] [PHYSICS] Saut effectué
{
  "velocityY": 12,
  "wasOnGround": true,
  "flyMode": false,
  "cooldownRemaining": 200,
  "jumpStartTime": 1753207987847,
  "timestamp": 12464
}
---
[1406] [2025-07-22T18:13:07.864Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 57,
  "timestamp": 12481
}
---
[1407] [2025-07-22T18:13:07.864Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 56,
  "timestamp": 12481
}
---
[1408] [2025-07-22T18:13:07.880Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 12496
}
---
[1409] [2025-07-22T18:13:07.880Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12496
}
---
[1410] [2025-07-22T18:13:07.885Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12501
}
---
[1411] [2025-07-22T18:13:07.886Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12502
}
---
[1412] [2025-07-22T18:13:07.913Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 55,
  "timestamp": 12530
}
---
[1413] [2025-07-22T18:13:07.914Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 54,
  "timestamp": 12531
}
---
[1414] [2025-07-22T18:13:07.926Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 12543
}
---
[1415] [2025-07-22T18:13:07.926Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12543
}
---
[1416] [2025-07-22T18:13:07.927Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12544
}
---
[1417] [2025-07-22T18:13:07.927Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12544
}
---
[1418] [2025-07-22T18:13:07.964Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 53,
  "timestamp": 12580
}
---
[1419] [2025-07-22T18:13:07.964Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 52,
  "timestamp": 12581
}
---
[1420] [2025-07-22T18:13:07.985Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 12602
}
---
[1421] [2025-07-22T18:13:07.985Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12602
}
---
[1422] [2025-07-22T18:13:07.987Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 12603
}
---
[1423] [2025-07-22T18:13:07.987Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12604
}
---
[1424] [2025-07-22T18:13:08.014Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 51,
  "timestamp": 12630
}
---
[1425] [2025-07-22T18:13:08.014Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 50,
  "timestamp": 12631
}
---
[1426] [2025-07-22T18:13:08.027Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 12643
}
---
[1427] [2025-07-22T18:13:08.027Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12644
}
---
[1428] [2025-07-22T18:13:08.029Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12645
}
---
[1429] [2025-07-22T18:13:08.029Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12645
}
---
[1430] [2025-07-22T18:13:08.063Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 49,
  "timestamp": 12680
}
---
[1431] [2025-07-22T18:13:08.064Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 48,
  "timestamp": 12680
}
---
[1432] [2025-07-22T18:13:08.076Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 12692
}
---
[1433] [2025-07-22T18:13:08.076Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12693
}
---
[1434] [2025-07-22T18:13:08.078Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12694
}
---
[1435] [2025-07-22T18:13:08.078Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12694
}
---
[1436] [2025-07-22T18:13:08.114Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 47,
  "timestamp": 12731
}
---
[1437] [2025-07-22T18:13:08.115Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 46,
  "timestamp": 12731
}
---
[1438] [2025-07-22T18:13:08.126Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 12742
}
---
[1439] [2025-07-22T18:13:08.126Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12742
}
---
[1440] [2025-07-22T18:13:08.127Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 12743
}
---
[1441] [2025-07-22T18:13:08.127Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12744
}
---
[1442] [2025-07-22T18:13:08.164Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 45,
  "timestamp": 12780
}
---
[1443] [2025-07-22T18:13:08.164Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 44,
  "timestamp": 12782
}
---
[1444] [2025-07-22T18:13:08.177Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12794
}
---
[1445] [2025-07-22T18:13:08.177Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12794
}
---
[1446] [2025-07-22T18:13:08.184Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 12800
}
---
[1447] [2025-07-22T18:13:08.184Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12800
}
---
[1448] [2025-07-22T18:13:08.213Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 43,
  "timestamp": 12831
}
---
[1449] [2025-07-22T18:13:08.214Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 42,
  "timestamp": 12831
}
---
[1450] [2025-07-22T18:13:08.228Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 12845
}
---
[1451] [2025-07-22T18:13:08.228Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12845
}
---
[1452] [2025-07-22T18:13:08.229Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 12846
}
---
[1453] [2025-07-22T18:13:08.229Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12846
}
---
[1454] [2025-07-22T18:13:08.264Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 41,
  "timestamp": 12881
}
---
[1455] [2025-07-22T18:13:08.265Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 40,
  "timestamp": 12881
}
---
[1456] [2025-07-22T18:13:08.277Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 12894
}
---
[1457] [2025-07-22T18:13:08.277Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12895
}
---
[1458] [2025-07-22T18:13:08.279Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12896
}
---
[1459] [2025-07-22T18:13:08.279Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12896
}
---
[1460] [2025-07-22T18:13:08.313Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 39,
  "timestamp": 12930
}
---
[1461] [2025-07-22T18:13:08.314Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 38,
  "timestamp": 12931
}
---
[1462] [2025-07-22T18:13:08.325Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 12943
}
---
[1463] [2025-07-22T18:13:08.327Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12943
}
---
[1464] [2025-07-22T18:13:08.327Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 12944
}
---
[1465] [2025-07-22T18:13:08.328Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12944
}
---
[1466] [2025-07-22T18:13:08.364Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 37,
  "timestamp": 12980
}
---
[1467] [2025-07-22T18:13:08.364Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 36,
  "timestamp": 12980
}
---
[1468] [2025-07-22T18:13:08.376Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 12991
}
---
[1469] [2025-07-22T18:13:08.376Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12993
}
---
[1470] [2025-07-22T18:13:08.377Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 12993
}
---
[1471] [2025-07-22T18:13:08.377Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 12993
}
---
[1472] [2025-07-22T18:13:08.414Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 35,
  "timestamp": 13030
}
---
[1473] [2025-07-22T18:13:08.414Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 34,
  "timestamp": 13031
}
---
[1474] [2025-07-22T18:13:08.427Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 13044
}
---
[1475] [2025-07-22T18:13:08.427Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13044
}
---
[1476] [2025-07-22T18:13:08.429Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 13045
}
---
[1477] [2025-07-22T18:13:08.429Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13045
}
---
[1478] [2025-07-22T18:13:08.464Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 33,
  "timestamp": 13081
}
---
[1479] [2025-07-22T18:13:08.464Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 32,
  "timestamp": 13081
}
---
[1480] [2025-07-22T18:13:08.465Z] [DEBUG] État de la scène
{
  "sceneObjects": 198,
  "cameraPosition": {
    "x": 9.6,
    "y": 80.11,
    "z": -60.35
  },
  "fps": 60,
  "frameCount": 720
}
---
[1481] [2025-07-22T18:13:08.476Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 13092
}
---
[1482] [2025-07-22T18:13:08.476Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13092
}
---
[1483] [2025-07-22T18:13:08.478Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 13094
}
---
[1484] [2025-07-22T18:13:08.478Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13094
}
---
[1485] [2025-07-22T18:13:08.514Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 31,
  "timestamp": 13130
}
---
[1486] [2025-07-22T18:13:08.515Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 30,
  "timestamp": 13131
}
---
[1487] [2025-07-22T18:13:08.526Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 13143
}
---
[1488] [2025-07-22T18:13:08.526Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13143
}
---
[1489] [2025-07-22T18:13:08.527Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 13144
}
---
[1490] [2025-07-22T18:13:08.527Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13144
}
---
[1491] [2025-07-22T18:13:08.564Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 29,
  "timestamp": 13180
}
---
[1492] [2025-07-22T18:13:08.564Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 28,
  "timestamp": 13181
}
---
[1493] [2025-07-22T18:13:08.575Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 13192
}
---
[1494] [2025-07-22T18:13:08.576Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13192
}
---
[1495] [2025-07-22T18:13:08.577Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 13193
}
---
[1496] [2025-07-22T18:13:08.577Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13193
}
---
[1497] [2025-07-22T18:13:08.614Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 27,
  "timestamp": 13231
}
---
[1498] [2025-07-22T18:13:08.615Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 26,
  "timestamp": 13231
}
---
[1499] [2025-07-22T18:13:08.628Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 13244
}
---
[1500] [2025-07-22T18:13:08.628Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13245
}
---
[1501] [2025-07-22T18:13:08.630Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 13246
}
---
[1502] [2025-07-22T18:13:08.630Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13247
}
---
[1503] [2025-07-22T18:13:08.664Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 25,
  "timestamp": 13280
}
---
[1504] [2025-07-22T18:13:08.664Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 24,
  "timestamp": 13281
}
---
[1505] [2025-07-22T18:13:08.677Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 13293
}
---
[1506] [2025-07-22T18:13:08.677Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13294
}
---
[1507] [2025-07-22T18:13:08.678Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 13295
}
---
[1508] [2025-07-22T18:13:08.678Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13295
}
---
[1509] [2025-07-22T18:13:08.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 23,
  "timestamp": 13331
}
---
[1510] [2025-07-22T18:13:08.715Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 22,
  "timestamp": 13331
}
---
[1511] [2025-07-22T18:13:08.728Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 13345
}
---
[1512] [2025-07-22T18:13:08.728Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13346
}
---
[1513] [2025-07-22T18:13:08.730Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 13347
}
---
[1514] [2025-07-22T18:13:08.730Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13347
}
---
[1515] [2025-07-22T18:13:08.764Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 21,
  "timestamp": 13380
}
---
[1516] [2025-07-22T18:13:08.764Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 13381
}
---
[1517] [2025-07-22T18:13:08.777Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 13394
}
---
[1518] [2025-07-22T18:13:08.778Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13395
}
---
[1519] [2025-07-22T18:13:08.779Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 13396
}
---
[1520] [2025-07-22T18:13:08.780Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13396
}
---
[1521] [2025-07-22T18:13:08.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 13430
}
---
[1522] [2025-07-22T18:13:08.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 13430
}
---
[1523] [2025-07-22T18:13:08.827Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 13443
}
---
[1524] [2025-07-22T18:13:08.827Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13444
}
---
[1525] [2025-07-22T18:13:08.828Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 13445
}
---
[1526] [2025-07-22T18:13:08.829Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13445
}
---
[1527] [2025-07-22T18:13:08.863Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 17,
  "timestamp": 13480
}
---
[1528] [2025-07-22T18:13:08.864Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 16,
  "timestamp": 13481
}
---
[1529] [2025-07-22T18:13:08.877Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 13494
}
---
[1530] [2025-07-22T18:13:08.878Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13494
}
---
[1531] [2025-07-22T18:13:08.879Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 13496
}
---
[1532] [2025-07-22T18:13:08.880Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 0
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13496
}
---
[1533] [2025-07-22T18:13:08.914Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 15,
  "timestamp": 13530
}
---
[1534] [2025-07-22T18:13:08.914Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 14,
  "timestamp": 13531
}
---
[1535] [2025-07-22T18:13:08.927Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 13544
}
---
[1536] [2025-07-22T18:13:08.928Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13544
}
---
[1537] [2025-07-22T18:13:08.929Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 13545
}
---
[1538] [2025-07-22T18:13:08.929Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13545
}
---
[1539] [2025-07-22T18:13:08.963Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 13,
  "timestamp": 13581
}
---
[1540] [2025-07-22T18:13:08.964Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 13581
}
---
[1541] [2025-07-22T18:13:08.983Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 13599
}
---
[1542] [2025-07-22T18:13:08.983Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13600
}
---
[1543] [2025-07-22T18:13:08.987Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 13603
}
---
[1544] [2025-07-22T18:13:08.987Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13604
}
---
[1545] [2025-07-22T18:13:09.013Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 0,
    "z": -5
  },
  "oldChunk": {
    "x": 0,
    "z": -4
  },
  "timestamp": 13630
}
---
[1546] [2025-07-22T18:13:09.064Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 32,
  "timestamp": 13680
}
---
[1547] [2025-07-22T18:13:09.064Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 31,
  "timestamp": 13680
}
---
[1548] [2025-07-22T18:13:09.076Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 13692
}
---
[1549] [2025-07-22T18:13:09.076Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13692
}
---
[1550] [2025-07-22T18:13:09.077Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 13694
}
---
[1551] [2025-07-22T18:13:09.077Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13694
}
---
[1552] [2025-07-22T18:13:09.097Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 30,
  "timestamp": 13714
}
---
[1553] [2025-07-22T18:13:09.097Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 29,
  "timestamp": 13714
}
---
[1554] [2025-07-22T18:13:09.109Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 13726
}
---
[1555] [2025-07-22T18:13:09.111Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13727
}
---
[1556] [2025-07-22T18:13:09.111Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 13728
}
---
[1557] [2025-07-22T18:13:09.111Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13728
}
---
[1558] [2025-07-22T18:13:09.146Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 28,
  "timestamp": 13763
}
---
[1559] [2025-07-22T18:13:09.148Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 27,
  "timestamp": 13764
}
---
[1560] [2025-07-22T18:13:09.160Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 13777
}
---
[1561] [2025-07-22T18:13:09.160Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13777
}
---
[1562] [2025-07-22T18:13:09.161Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 13778
}
---
[1563] [2025-07-22T18:13:09.161Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13779
}
---
[1564] [2025-07-22T18:13:09.197Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 26,
  "timestamp": 13814
}
---
[1565] [2025-07-22T18:13:09.197Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 25,
  "timestamp": 13814
}
---
[1566] [2025-07-22T18:13:09.210Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 13827
}
---
[1567] [2025-07-22T18:13:09.210Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13827
}
---
[1568] [2025-07-22T18:13:09.212Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 13828
}
---
[1569] [2025-07-22T18:13:09.212Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13829
}
---
[1570] [2025-07-22T18:13:09.246Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 24,
  "timestamp": 13863
}
---
[1571] [2025-07-22T18:13:09.247Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 23,
  "timestamp": 13863
}
---
[1572] [2025-07-22T18:13:09.262Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 13878
}
---
[1573] [2025-07-22T18:13:09.262Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13878
}
---
[1574] [2025-07-22T18:13:09.263Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 13880
}
---
[1575] [2025-07-22T18:13:09.264Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13880
}
---
[1576] [2025-07-22T18:13:09.298Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 22,
  "timestamp": 13914
}
---
[1577] [2025-07-22T18:13:09.298Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": 6
  },
  "priority": 2,
  "queueRemaining": 21,
  "timestamp": 13914
}
---
[1578] [2025-07-22T18:13:09.310Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 13925
}
---
[1579] [2025-07-22T18:13:09.310Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13927
}
---
[1580] [2025-07-22T18:13:09.310Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": 6
  },
  "blocksReceived": 32768,
  "timestamp": 13927
}
---
[1581] [2025-07-22T18:13:09.311Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": 6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13927
}
---
[1582] [2025-07-22T18:13:09.348Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 13964
}
---
[1583] [2025-07-22T18:13:09.348Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 13965
}
---
[1584] [2025-07-22T18:13:09.368Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 13984
}
---
[1585] [2025-07-22T18:13:09.368Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13985
}
---
[1586] [2025-07-22T18:13:09.371Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 13988
}
---
[1587] [2025-07-22T18:13:09.371Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 13988
}
---
[1588] [2025-07-22T18:13:09.397Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 14013
}
---
[1589] [2025-07-22T18:13:09.398Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 17,
  "timestamp": 14014
}
---
[1590] [2025-07-22T18:13:09.410Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14027
}
---
[1591] [2025-07-22T18:13:09.410Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14027
}
---
[1592] [2025-07-22T18:13:09.411Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14028
}
---
[1593] [2025-07-22T18:13:09.411Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14029
}
---
[1594] [2025-07-22T18:13:09.447Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 16,
  "timestamp": 14064
}
---
[1595] [2025-07-22T18:13:09.448Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 15,
  "timestamp": 14065
}
---
[1596] [2025-07-22T18:13:09.459Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14075
}
---
[1597] [2025-07-22T18:13:09.459Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14076
}
---
[1598] [2025-07-22T18:13:09.460Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14077
}
---
[1599] [2025-07-22T18:13:09.460Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14077
}
---
[1600] [2025-07-22T18:13:09.497Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 14,
  "timestamp": 14114
}
---
[1601] [2025-07-22T18:13:09.498Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 13,
  "timestamp": 14115
}
---
[1602] [2025-07-22T18:13:09.519Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14135
}
---
[1603] [2025-07-22T18:13:09.519Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14136
}
---
[1604] [2025-07-22T18:13:09.520Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14137
}
---
[1605] [2025-07-22T18:13:09.520Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14137
}
---
[1606] [2025-07-22T18:13:09.547Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 14164
}
---
[1607] [2025-07-22T18:13:09.548Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 11,
  "timestamp": 14165
}
---
[1608] [2025-07-22T18:13:09.568Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14185
}
---
[1609] [2025-07-22T18:13:09.569Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14186
}
---
[1610] [2025-07-22T18:13:09.570Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14187
}
---
[1611] [2025-07-22T18:13:09.570Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14187
}
---
[1612] [2025-07-22T18:13:09.597Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 10,
  "timestamp": 14214
}
---
[1613] [2025-07-22T18:13:09.597Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 9,
  "timestamp": 14214
}
---
[1614] [2025-07-22T18:13:09.610Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14227
}
---
[1615] [2025-07-22T18:13:09.611Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14227
}
---
[1616] [2025-07-22T18:13:09.612Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14229
}
---
[1617] [2025-07-22T18:13:09.612Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14229
}
---
[1618] [2025-07-22T18:13:09.647Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 8,
  "timestamp": 14263
}
---
[1619] [2025-07-22T18:13:09.648Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 7,
  "timestamp": 14265
}
---
[1620] [2025-07-22T18:13:09.660Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14276
}
---
[1621] [2025-07-22T18:13:09.660Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14277
}
---
[1622] [2025-07-22T18:13:09.662Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14279
}
---
[1623] [2025-07-22T18:13:09.662Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14279
}
---
[1624] [2025-07-22T18:13:09.697Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 6,
  "timestamp": 14314
}
---
[1625] [2025-07-22T18:13:09.697Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 5,
  "timestamp": 14314
}
---
[1626] [2025-07-22T18:13:09.710Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14326
}
---
[1627] [2025-07-22T18:13:09.710Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14327
}
---
[1628] [2025-07-22T18:13:09.712Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14328
}
---
[1629] [2025-07-22T18:13:09.712Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14328
}
---
[1630] [2025-07-22T18:13:09.747Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 4,
  "timestamp": 14363
}
---
[1631] [2025-07-22T18:13:09.748Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 3,
  "timestamp": 14364
}
---
[1632] [2025-07-22T18:13:09.759Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14376
}
---
[1633] [2025-07-22T18:13:09.760Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14376
}
---
[1634] [2025-07-22T18:13:09.763Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14380
}
---
[1635] [2025-07-22T18:13:09.764Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14380
}
---
[1636] [2025-07-22T18:13:09.797Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 2,
  "timestamp": 14414
}
---
[1637] [2025-07-22T18:13:09.801Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -10,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 1,
  "timestamp": 14419
}
---
[1638] [2025-07-22T18:13:09.819Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14435
}
---
[1639] [2025-07-22T18:13:09.820Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14437
}
---
[1640] [2025-07-22T18:13:09.822Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -10,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14439
}
---
[1641] [2025-07-22T18:13:09.823Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -10,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14439
}
---
[1642] [2025-07-22T18:13:09.848Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 0,
  "timestamp": 14464
}
---
[1643] [2025-07-22T18:13:09.859Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 14476
}
---
[1644] [2025-07-22T18:13:09.860Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14476
}
---
[1645] [2025-07-22T18:13:10.246Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": -1,
    "z": -5
  },
  "oldChunk": {
    "x": 0,
    "z": -5
  },
  "timestamp": 14864
}
---
[1646] [2025-07-22T18:13:10.289Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 14906
}
---
[1647] [2025-07-22T18:13:10.290Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 14906
}
---
[1648] [2025-07-22T18:13:10.303Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 14920
}
---
[1649] [2025-07-22T18:13:10.303Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14920
}
---
[1650] [2025-07-22T18:13:10.305Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 14921
}
---
[1651] [2025-07-22T18:13:10.305Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14922
}
---
[1652] [2025-07-22T18:13:10.330Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 14947
}
---
[1653] [2025-07-22T18:13:10.331Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 17,
  "timestamp": 14947
}
---
[1654] [2025-07-22T18:13:10.344Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 14961
}
---
[1655] [2025-07-22T18:13:10.344Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14961
}
---
[1656] [2025-07-22T18:13:10.345Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 14962
}
---
[1657] [2025-07-22T18:13:10.345Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 14962
}
---
[1658] [2025-07-22T18:13:10.381Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 16,
  "timestamp": 14997
}
---
[1659] [2025-07-22T18:13:10.381Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 15,
  "timestamp": 14998
}
---
[1660] [2025-07-22T18:13:10.393Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 15010
}
---
[1661] [2025-07-22T18:13:10.393Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15010
}
---
[1662] [2025-07-22T18:13:10.394Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 15011
}
---
[1663] [2025-07-22T18:13:10.395Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15011
}
---
[1664] [2025-07-22T18:13:10.431Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 14,
  "timestamp": 15046
}
---
[1665] [2025-07-22T18:13:10.431Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 13,
  "timestamp": 15047
}
---
[1666] [2025-07-22T18:13:10.445Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 15062
}
---
[1667] [2025-07-22T18:13:10.445Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15062
}
---
[1668] [2025-07-22T18:13:10.453Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 15069
}
---
[1669] [2025-07-22T18:13:10.453Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15069
}
---
[1670] [2025-07-22T18:13:10.481Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 15097
}
---
[1671] [2025-07-22T18:13:10.481Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 11,
  "timestamp": 15098
}
---
[1672] [2025-07-22T18:13:10.494Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 15110
}
---
[1673] [2025-07-22T18:13:10.494Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15110
}
---
[1674] [2025-07-22T18:13:10.495Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 15111
}
---
[1675] [2025-07-22T18:13:10.495Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15112
}
---
[1676] [2025-07-22T18:13:10.531Z] [PHYSICS] Saut effectué
{
  "velocityY": 12,
  "wasOnGround": true,
  "flyMode": false,
  "cooldownRemaining": 200,
  "jumpStartTime": 1753207990530,
  "timestamp": 15147
}
---
[1677] [2025-07-22T18:13:10.531Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 10,
  "timestamp": 15148
}
---
[1678] [2025-07-22T18:13:10.532Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 9,
  "timestamp": 15148
}
---
[1679] [2025-07-22T18:13:10.532Z] [DEBUG] État de la scène
{
  "sceneObjects": 213,
  "cameraPosition": {
    "x": -2,
    "y": 71.9,
    "z": -75.24
  },
  "fps": 60,
  "frameCount": 840
}
---
[1680] [2025-07-22T18:13:10.544Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 15160
}
---
[1681] [2025-07-22T18:13:10.544Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": 0
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15160
}
---
[1682] [2025-07-22T18:13:10.545Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 15162
}
---
[1683] [2025-07-22T18:13:10.546Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15162
}
---
[1684] [2025-07-22T18:13:10.581Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 8,
  "timestamp": 15197
}
---
[1685] [2025-07-22T18:13:10.581Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 7,
  "timestamp": 15197
}
---
[1686] [2025-07-22T18:13:10.593Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 15210
}
---
[1687] [2025-07-22T18:13:10.594Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15210
}
---
[1688] [2025-07-22T18:13:10.595Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 15212
}
---
[1689] [2025-07-22T18:13:10.595Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15212
}
---
[1690] [2025-07-22T18:13:10.631Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 6,
  "timestamp": 15247
}
---
[1691] [2025-07-22T18:13:10.631Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 5,
  "timestamp": 15248
}
---
[1692] [2025-07-22T18:13:10.643Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 15260
}
---
[1693] [2025-07-22T18:13:10.644Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15261
}
---
[1694] [2025-07-22T18:13:10.645Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 15262
}
---
[1695] [2025-07-22T18:13:10.645Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15262
}
---
[1696] [2025-07-22T18:13:10.681Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 4,
  "timestamp": 15297
}
---
[1697] [2025-07-22T18:13:10.681Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 3,
  "timestamp": 15298
}
---
[1698] [2025-07-22T18:13:10.694Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 15310
}
---
[1699] [2025-07-22T18:13:10.694Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15311
}
---
[1700] [2025-07-22T18:13:10.695Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 15312
}
---
[1701] [2025-07-22T18:13:10.695Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15312
}
---
[1702] [2025-07-22T18:13:10.731Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 2,
  "timestamp": 15347
}
---
[1703] [2025-07-22T18:13:10.731Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 1,
  "timestamp": 15348
}
---
[1704] [2025-07-22T18:13:10.743Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 15359
}
---
[1705] [2025-07-22T18:13:10.744Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15360
}
---
[1706] [2025-07-22T18:13:10.745Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 15361
}
---
[1707] [2025-07-22T18:13:10.745Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15361
}
---
[1708] [2025-07-22T18:13:10.781Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -11,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 0,
  "timestamp": 15397
}
---
[1709] [2025-07-22T18:13:10.794Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -11,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 15411
}
---
[1710] [2025-07-22T18:13:10.795Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -11,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 15411
}
---
[1711] [2025-07-22T18:13:11.481Z] [DEBUG] Statistiques de performance
{
  "fps": 20,
  "chunksGenerated": 544,
  "chunksRendered": 210,
  "chunksInQueue": 0,
  "chunksBeingGenerated": 0
}
---
[1712] [2025-07-22T18:13:12.531Z] [DEBUG] État de la scène
{
  "sceneObjects": 213,
  "cameraPosition": {
    "x": -2.12,
    "y": 71.7,
    "z": -75.36
  },
  "fps": 60,
  "frameCount": 960
}
---
[1713] [2025-07-22T18:13:14.514Z] [PHYSICS] Saut effectué
{
  "velocityY": 12,
  "wasOnGround": true,
  "flyMode": false,
  "cooldownRemaining": 200,
  "jumpStartTime": 1753207994514,
  "timestamp": 19130
}
---
[1714] [2025-07-22T18:13:14.531Z] [DEBUG] État de la scène
{
  "sceneObjects": 213,
  "cameraPosition": {
    "x": -0.64,
    "y": 66.1,
    "z": -76.45
  },
  "fps": 60,
  "frameCount": 1080
}
---
[1715] [2025-07-22T18:13:16.531Z] [DEBUG] État de la scène
{
  "sceneObjects": 213,
  "cameraPosition": {
    "x": -0.64,
    "y": 65.7,
    "z": -76.45
  },
  "fps": 60,
  "frameCount": 1200
}
---
[1716] [2025-07-22T18:13:17.031Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 0,
    "z": -5
  },
  "oldChunk": {
    "x": -1,
    "z": -5
  },
  "timestamp": 21647
}
---
[1717] [2025-07-22T18:13:17.248Z] [PHYSICS] Saut effectué
{
  "velocityY": 12,
  "wasOnGround": true,
  "flyMode": false,
  "cooldownRemaining": 200,
  "jumpStartTime": 1753207997248,
  "timestamp": 21864
}
---
[1718] [2025-07-22T18:13:18.531Z] [DEBUG] État de la scène
{
  "sceneObjects": 213,
  "cameraPosition": {
    "x": 14.89,
    "y": 80.01,
    "z": -78.47
  },
  "fps": 60,
  "frameCount": 1320
}
---
[1719] [2025-07-22T18:13:18.680Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 1,
    "z": -5
  },
  "oldChunk": {
    "x": 0,
    "z": -5
  },
  "timestamp": 23297
}
---
[1720] [2025-07-22T18:13:18.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 3,
  "timestamp": 23331
}
---
[1721] [2025-07-22T18:13:18.715Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 2,
  "timestamp": 23331
}
---
[1722] [2025-07-22T18:13:18.729Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 23345
}
---
[1723] [2025-07-22T18:13:18.729Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 23345
}
---
[1724] [2025-07-22T18:13:18.730Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 23347
}
---
[1725] [2025-07-22T18:13:18.730Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 23347
}
---
[1726] [2025-07-22T18:13:18.746Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 1,
  "timestamp": 23364
}
---
[1727] [2025-07-22T18:13:18.748Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": 5
  },
  "priority": 2,
  "queueRemaining": 0,
  "timestamp": 23364
}
---
[1728] [2025-07-22T18:13:18.759Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 23376
}
---
[1729] [2025-07-22T18:13:18.760Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 23376
}
---
[1730] [2025-07-22T18:13:18.761Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": 5
  },
  "blocksReceived": 32768,
  "timestamp": 23377
}
---
[1731] [2025-07-22T18:13:18.761Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": 5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 23378
}
---
[1732] [2025-07-22T18:13:19.697Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 1,
    "z": -6
  },
  "oldChunk": {
    "x": 1,
    "z": -5
  },
  "timestamp": 24313
}
---
[1733] [2025-07-22T18:13:19.747Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 1,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 24363
}
---
[1734] [2025-07-22T18:13:19.747Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 0,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 24364
}
---
[1735] [2025-07-22T18:13:19.759Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 0,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24375
}
---
[1736] [2025-07-22T18:13:19.760Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 0,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24376
}
---
[1737] [2025-07-22T18:13:19.762Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 1,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24378
}
---
[1738] [2025-07-22T18:13:19.762Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 1,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24378
}
---
[1739] [2025-07-22T18:13:19.781Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 2,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 24398
}
---
[1740] [2025-07-22T18:13:19.782Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -1,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 17,
  "timestamp": 24398
}
---
[1741] [2025-07-22T18:13:19.795Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 2,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24411
}
---
[1742] [2025-07-22T18:13:19.795Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 2,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24411
}
---
[1743] [2025-07-22T18:13:19.796Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -1,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24412
}
---
[1744] [2025-07-22T18:13:19.796Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -1,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24413
}
---
[1745] [2025-07-22T18:13:19.831Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 3,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 16,
  "timestamp": 24447
}
---
[1746] [2025-07-22T18:13:19.831Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -2,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 15,
  "timestamp": 24447
}
---
[1747] [2025-07-22T18:13:19.852Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 3,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24468
}
---
[1748] [2025-07-22T18:13:19.852Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 3,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24469
}
---
[1749] [2025-07-22T18:13:19.853Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -2,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24470
}
---
[1750] [2025-07-22T18:13:19.853Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -2,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24470
}
---
[1751] [2025-07-22T18:13:19.881Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 4,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 14,
  "timestamp": 24497
}
---
[1752] [2025-07-22T18:13:19.881Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -3,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 13,
  "timestamp": 24498
}
---
[1753] [2025-07-22T18:13:19.894Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 4,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24510
}
---
[1754] [2025-07-22T18:13:19.894Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 4,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24510
}
---
[1755] [2025-07-22T18:13:19.895Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -3,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24511
}
---
[1756] [2025-07-22T18:13:19.895Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -3,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24511
}
---
[1757] [2025-07-22T18:13:19.931Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 5,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 24547
}
---
[1758] [2025-07-22T18:13:19.931Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -4,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 11,
  "timestamp": 24547
}
---
[1759] [2025-07-22T18:13:19.952Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 5,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24569
}
---
[1760] [2025-07-22T18:13:19.953Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 5,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24570
}
---
[1761] [2025-07-22T18:13:19.954Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -4,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24571
}
---
[1762] [2025-07-22T18:13:19.955Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -4,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24571
}
---
[1763] [2025-07-22T18:13:19.980Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 6,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 10,
  "timestamp": 24597
}
---
[1764] [2025-07-22T18:13:19.980Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -5,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 9,
  "timestamp": 24597
}
---
[1765] [2025-07-22T18:13:19.994Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 6,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24610
}
---
[1766] [2025-07-22T18:13:19.994Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 6,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24610
}
---
[1767] [2025-07-22T18:13:19.995Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -5,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24611
}
---
[1768] [2025-07-22T18:13:19.995Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -5,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24612
}
---
[1769] [2025-07-22T18:13:20.030Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 7,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 8,
  "timestamp": 24647
}
---
[1770] [2025-07-22T18:13:20.031Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -6,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 7,
  "timestamp": 24647
}
---
[1771] [2025-07-22T18:13:20.043Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 7,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24659
}
---
[1772] [2025-07-22T18:13:20.043Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 7,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24660
}
---
[1773] [2025-07-22T18:13:20.045Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -6,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24661
}
---
[1774] [2025-07-22T18:13:20.045Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -6,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24662
}
---
[1775] [2025-07-22T18:13:20.081Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 8,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 6,
  "timestamp": 24697
}
---
[1776] [2025-07-22T18:13:20.081Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -7,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 5,
  "timestamp": 24698
}
---
[1777] [2025-07-22T18:13:20.094Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 8,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24709
}
---
[1778] [2025-07-22T18:13:20.094Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 8,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24710
}
---
[1779] [2025-07-22T18:13:20.095Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -7,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24711
}
---
[1780] [2025-07-22T18:13:20.095Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -7,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24711
}
---
[1781] [2025-07-22T18:13:20.131Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 9,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 4,
  "timestamp": 24747
}
---
[1782] [2025-07-22T18:13:20.131Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -8,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 3,
  "timestamp": 24748
}
---
[1783] [2025-07-22T18:13:20.142Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 9,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24759
}
---
[1784] [2025-07-22T18:13:20.144Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 9,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24760
}
---
[1785] [2025-07-22T18:13:20.145Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -8,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24761
}
---
[1786] [2025-07-22T18:13:20.145Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -8,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24761
}
---
[1787] [2025-07-22T18:13:20.181Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 10,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 2,
  "timestamp": 24797
}
---
[1788] [2025-07-22T18:13:20.181Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": -9,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 1,
  "timestamp": 24797
}
---
[1789] [2025-07-22T18:13:20.193Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 10,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24809
}
---
[1790] [2025-07-22T18:13:20.193Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 10,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24810
}
---
[1791] [2025-07-22T18:13:20.194Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": -9,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24811
}
---
[1792] [2025-07-22T18:13:20.194Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": -9,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24811
}
---
[1793] [2025-07-22T18:13:20.230Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 11,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 0,
  "timestamp": 24847
}
---
[1794] [2025-07-22T18:13:20.243Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 11,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 24859
}
---
[1795] [2025-07-22T18:13:20.243Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 11,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24860
}
---
[1796] [2025-07-22T18:13:20.280Z] [CHUNK] Joueur déplacé vers nouveau chunk
{
  "newChunk": {
    "x": 2,
    "z": -6
  },
  "oldChunk": {
    "x": 1,
    "z": -6
  },
  "timestamp": 24897
}
---
[1797] [2025-07-22T18:13:20.326Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -6
  },
  "priority": 2,
  "queueRemaining": 20,
  "timestamp": 24943
}
---
[1798] [2025-07-22T18:13:20.327Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -7
  },
  "priority": 2,
  "queueRemaining": 19,
  "timestamp": 24943
}
---
[1799] [2025-07-22T18:13:20.341Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -7
  },
  "blocksReceived": 32768,
  "timestamp": 24957
}
---
[1800] [2025-07-22T18:13:20.341Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -7
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24958
}
---
[1801] [2025-07-22T18:13:20.342Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -6
  },
  "blocksReceived": 32768,
  "timestamp": 24959
}
---
[1802] [2025-07-22T18:13:20.342Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -6
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24959
}
---
[1803] [2025-07-22T18:13:20.364Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -5
  },
  "priority": 2,
  "queueRemaining": 18,
  "timestamp": 24980
}
---
[1804] [2025-07-22T18:13:20.364Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -8
  },
  "priority": 2,
  "queueRemaining": 17,
  "timestamp": 24980
}
---
[1805] [2025-07-22T18:13:20.375Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -8
  },
  "blocksReceived": 32768,
  "timestamp": 24992
}
---
[1806] [2025-07-22T18:13:20.376Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -8
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24992
}
---
[1807] [2025-07-22T18:13:20.377Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -5
  },
  "blocksReceived": 32768,
  "timestamp": 24994
}
---
[1808] [2025-07-22T18:13:20.377Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -5
  },
  "reason": "Hors distance de rendu",
  "timestamp": 24994
}
---
[1809] [2025-07-22T18:13:20.414Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -4
  },
  "priority": 2,
  "queueRemaining": 16,
  "timestamp": 25031
}
---
[1810] [2025-07-22T18:13:20.415Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -9
  },
  "priority": 2,
  "queueRemaining": 15,
  "timestamp": 25031
}
---
[1811] [2025-07-22T18:13:20.426Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -4
  },
  "blocksReceived": 32768,
  "timestamp": 25043
}
---
[1812] [2025-07-22T18:13:20.427Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25043
}
---
[1813] [2025-07-22T18:13:20.428Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -9
  },
  "blocksReceived": 32768,
  "timestamp": 25044
}
---
[1814] [2025-07-22T18:13:20.428Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -9
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25044
}
---
[1815] [2025-07-22T18:13:20.431Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1816] [2025-07-22T18:13:20.447Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1817] [2025-07-22T18:13:20.464Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1818] [2025-07-22T18:13:20.465Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -3
  },
  "priority": 2,
  "queueRemaining": 14,
  "timestamp": 25081
}
---
[1819] [2025-07-22T18:13:20.465Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -10
  },
  "priority": 2,
  "queueRemaining": 13,
  "timestamp": 25081
}
---
[1820] [2025-07-22T18:13:20.476Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -3
  },
  "blocksReceived": 32768,
  "timestamp": 25092
}
---
[1821] [2025-07-22T18:13:20.477Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25093
}
---
[1822] [2025-07-22T18:13:20.477Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -10
  },
  "blocksReceived": 32768,
  "timestamp": 25095
}
---
[1823] [2025-07-22T18:13:20.478Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -10
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25095
}
---
[1824] [2025-07-22T18:13:20.481Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1825] [2025-07-22T18:13:20.497Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1826] [2025-07-22T18:13:20.514Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1827] [2025-07-22T18:13:20.514Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -2
  },
  "priority": 2,
  "queueRemaining": 12,
  "timestamp": 25130
}
---
[1828] [2025-07-22T18:13:20.514Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -11
  },
  "priority": 2,
  "queueRemaining": 11,
  "timestamp": 25130
}
---
[1829] [2025-07-22T18:13:20.528Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -11
  },
  "blocksReceived": 32768,
  "timestamp": 25144
}
---
[1830] [2025-07-22T18:13:20.528Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -11
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25144
}
---
[1831] [2025-07-22T18:13:20.529Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -2
  },
  "blocksReceived": 32768,
  "timestamp": 25145
}
---
[1832] [2025-07-22T18:13:20.529Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25146
}
---
[1833] [2025-07-22T18:13:20.530Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1834] [2025-07-22T18:13:20.547Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1835] [2025-07-22T18:13:20.564Z] [DEBUG] Mouvement bloqué par obstacle
{
  "blockType": 0,
  "reason": "Bloc non-grimpable"
}
---
[1836] [2025-07-22T18:13:20.564Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -1
  },
  "priority": 2,
  "queueRemaining": 10,
  "timestamp": 25181
}
---
[1837] [2025-07-22T18:13:20.564Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -12
  },
  "priority": 2,
  "queueRemaining": 9,
  "timestamp": 25181
}
---
[1838] [2025-07-22T18:13:20.584Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -1
  },
  "blocksReceived": 32768,
  "timestamp": 25200
}
---
[1839] [2025-07-22T18:13:20.585Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25201
}
---
[1840] [2025-07-22T18:13:20.586Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -12
  },
  "blocksReceived": 32768,
  "timestamp": 25202
}
---
[1841] [2025-07-22T18:13:20.586Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -12
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25202
}
---
[1842] [2025-07-22T18:13:20.613Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": 0
  },
  "priority": 2,
  "queueRemaining": 8,
  "timestamp": 25231
}
---
[1843] [2025-07-22T18:13:20.615Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -13
  },
  "priority": 2,
  "queueRemaining": 7,
  "timestamp": 25231
}
---
[1844] [2025-07-22T18:13:20.615Z] [DEBUG] État de la scène
{
  "sceneObjects": 233,
  "cameraPosition": {
    "x": 34.05,
    "y": 70.7,
    "z": -81.07
  },
  "fps": 60,
  "frameCount": 1440
}
---
[1845] [2025-07-22T18:13:20.625Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": 0
  },
  "blocksReceived": 32768,
  "timestamp": 25242
}
---
[1846] [2025-07-22T18:13:20.625Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": 0
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25242
}
---
[1847] [2025-07-22T18:13:20.626Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -13
  },
  "blocksReceived": 32768,
  "timestamp": 25243
}
---
[1848] [2025-07-22T18:13:20.627Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -13
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25244
}
---
[1849] [2025-07-22T18:13:20.664Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": 1
  },
  "priority": 2,
  "queueRemaining": 6,
  "timestamp": 25281
}
---
[1850] [2025-07-22T18:13:20.665Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -14
  },
  "priority": 2,
  "queueRemaining": 5,
  "timestamp": 25281
}
---
[1851] [2025-07-22T18:13:20.676Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": 1
  },
  "blocksReceived": 32768,
  "timestamp": 25293
}
---
[1852] [2025-07-22T18:13:20.677Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": 1
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25293
}
---
[1853] [2025-07-22T18:13:20.678Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -14
  },
  "blocksReceived": 32768,
  "timestamp": 25294
}
---
[1854] [2025-07-22T18:13:20.678Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -14
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25294
}
---
[1855] [2025-07-22T18:13:20.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": 2
  },
  "priority": 2,
  "queueRemaining": 4,
  "timestamp": 25330
}
---
[1856] [2025-07-22T18:13:20.714Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -15
  },
  "priority": 2,
  "queueRemaining": 3,
  "timestamp": 25330
}
---
[1857] [2025-07-22T18:13:20.729Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -15
  },
  "blocksReceived": 32768,
  "timestamp": 25345
}
---
[1858] [2025-07-22T18:13:20.729Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -15
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25345
}
---
[1859] [2025-07-22T18:13:20.730Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": 2
  },
  "blocksReceived": 32768,
  "timestamp": 25346
}
---
[1860] [2025-07-22T18:13:20.730Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": 2
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25346
}
---
[1861] [2025-07-22T18:13:20.764Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": 3
  },
  "priority": 2,
  "queueRemaining": 2,
  "timestamp": 25380
}
---
[1862] [2025-07-22T18:13:20.765Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": -16
  },
  "priority": 2,
  "queueRemaining": 1,
  "timestamp": 25381
}
---
[1863] [2025-07-22T18:13:20.776Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": 3
  },
  "blocksReceived": 32768,
  "timestamp": 25392
}
---
[1864] [2025-07-22T18:13:20.776Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": 3
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25393
}
---
[1865] [2025-07-22T18:13:20.777Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": -16
  },
  "blocksReceived": 32768,
  "timestamp": 25394
}
---
[1866] [2025-07-22T18:13:20.777Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": -16
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25394
}
---
[1867] [2025-07-22T18:13:20.814Z] [CHUNK] Début génération chunk asynchrone
{
  "position": {
    "x": 12,
    "z": 4
  },
  "priority": 2,
  "queueRemaining": 0,
  "timestamp": 25431
}
---
[1868] [2025-07-22T18:13:20.826Z] [CHUNK] Données reçues du worker
{
  "position": {
    "x": 12,
    "z": 4
  },
  "blocksReceived": 32768,
  "timestamp": 25443
}
---
[1869] [2025-07-22T18:13:20.826Z] [CHUNK] Chunk stocké sans mesh
{
  "position": {
    "x": 12,
    "z": 4
  },
  "reason": "Hors distance de rendu",
  "timestamp": 25444
}
---
[1870] [2025-07-22T18:13:21.247Z] [PHYSICS] Saut effectué
{
  "velocityY": 12,
  "wasOnGround": true,
  "flyMode": false,
  "cooldownRemaining": 200,
  "jumpStartTime": 1753208001247,
  "timestamp": 25863
}
---
[1871] [2025-07-22T18:13:22.614Z] [DEBUG] État de la scène
{
  "sceneObjects": 233,
  "cameraPosition": {
    "x": 45.61,
    "y": 78.11,
    "z": -82.64
  },
  "fps": 60,
  "frameCount": 1560
}
---
[1872] [2025-07-22T18:13:22.941Z] [INFO] État du verrouillage du pointeur changé
{
  "isLocked": false,
  "element": "BODY"
}
---
[1873] [2025-07-22T18:13:24.533Z] [INFO] Raccourci F11 - Téléchargement logs
{
  "trigger": "keyboard_shortcut"
}
---
[1874] [2025-07-22T18:13:24.533Z] [INFO] Téléchargement manuel des logs demandé
{
  "trigger": "manual_download",
  "totalLogs": 1000
}