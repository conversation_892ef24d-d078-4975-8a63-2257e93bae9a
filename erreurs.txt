[DEBUG] État de la scène 
Object { sceneObjects: 241, cameraPosition: {…}, fps: 60, frameCount: 4200 }
Logger.js:102:21
[INFO] Click détecté, demande de verrouillage du pointeur 
Object {  }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: true, element: "BODY" }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 127341 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 127354 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 127369 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 127369 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 243, cameraPosition: {…}, fps: 60, frameCount: 4320 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128041 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128057 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128074 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128091 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128106 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128157 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128174 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128190 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128207 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128223 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128240 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128540 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128557 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128573 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128590 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128607 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128624 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128640 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128657 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128673 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128690 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128707 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 128990 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129007 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129508 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129524 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 129540 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 129553 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129564 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129564 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129575 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129590 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129640 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129657 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129674 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129691 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129707 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129724 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 129740 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 129787 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 129787 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 252, cameraPosition: {…}, fps: 60, frameCount: 4440 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129802 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129802 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129803 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129803 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129810 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 129826 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 129826 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129837 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129837 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129839 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129839 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129858 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129873 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 129874 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 129874 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129884 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129885 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129885 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129886 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129890 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 129907 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 129923 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 129925 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129934 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129934 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129936 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129937 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 129974 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 129974 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129984 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129984 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 129985 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 129985 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 130024 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 130025 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130033 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130033 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130035 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130035 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 130074 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 130074 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130084 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130085 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130085 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130086 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130090 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130107 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130124 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 130124 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 130124 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130133 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130134 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130134 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130136 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130140 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130157 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130174 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 130174 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 130175 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130183 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130184 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130185 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130185 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130191 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130207 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130223 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 130223 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 130224 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130234 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130235 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130236 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130236 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130241 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130257 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130274 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 130274 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 130283 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 130283 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130290 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130307 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130324 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130341 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130356 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130373 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130390 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130407 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130424 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130574 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130590 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130607 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130657 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130707 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130724 }
Logger.js:102:21
[DEBUG] Statistiques de performance 
Object { fps: 20, chunksGenerated: 675, chunksRendered: 249, chunksInQueue: 0, chunksBeingGenerated: 0 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130740 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130757 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130907 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130923 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130940 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130957 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130974 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 130990 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131008 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131057 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131073 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131191 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131206 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131257 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131274 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131290 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131308 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131324 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131340 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131357 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131374 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131391 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131407 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131424 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131440 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131457 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131474 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131490 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131507 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131524 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131574 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131590 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131607 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131657 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 131674 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 252, cameraPosition: {…}, fps: 60, frameCount: 4560 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 131824 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 20, timestamp: 131884 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 19, timestamp: 131884 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 131902 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 131902 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 131903 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 131903 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 18, timestamp: 131924 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 17, timestamp: 131924 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 131934 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 131934 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 131936 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 131936 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 16, timestamp: 131974 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 15, timestamp: 131975 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 131984 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 131986 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 131987 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 131987 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 14, timestamp: 132024 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 13, timestamp: 132025 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132034 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132034 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132035 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132035 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 12, timestamp: 132074 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 11, timestamp: 132074 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132083 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132084 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132086 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132087 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 10, timestamp: 132124 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 9, timestamp: 132124 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132134 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132134 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132136 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132136 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 8, timestamp: 132174 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 7, timestamp: 132174 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132183 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132184 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132185 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132185 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 6, timestamp: 132223 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 5, timestamp: 132224 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132233 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132233 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132235 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132235 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 4, timestamp: 132274 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 3, timestamp: 132274 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132284 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132284 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132285 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132286 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 2, timestamp: 132324 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 132324 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132344 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132345 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132346 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132347 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 132374 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132384 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132384 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 132523 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 132924 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 1, timestamp: 132944 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 132945 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132955 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132956 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 132957 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 132958 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133374 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133391 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133407 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133423 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133441 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133457 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133474 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133491 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133507 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133523 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133540 }
Logger.js:102:21
[PHYSICS] Saut effectué 
Object { velocityY: 15, wasOnGround: true, flyMode: false, timestamp: 133557 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 133674 }
Logger.js:102:21
[CHUNK] Début génération chunk asynchrone 
Object { position: {…}, priority: 2, queueRemaining: 0, timestamp: 133705 }
Logger.js:102:21
[CHUNK] Données reçues du worker 
Object { position: {…}, blocksReceived: 32768, timestamp: 133716 }
Logger.js:102:21
[CHUNK] Chunk stocké sans mesh 
Object { position: {…}, reason: "Hors distance de rendu", timestamp: 133717 }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 270, cameraPosition: {…}, fps: 60, frameCount: 4680 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 133991 }
Logger.js:102:21
[CHUNK] Joueur déplacé vers nouveau chunk 
Object { newChunk: {…}, oldChunk: {…}, timestamp: 134440 }
Logger.js:102:21
[INFO] État du verrouillage du pointeur changé 
Object { isLocked: false, element: "BODY" }
Logger.js:102:21
[DEBUG] État de la scène 
Object { sceneObjects: 270, cameraPosition: {…}, fps: 60, frameCount: 4800 }
Logger.js:102:21
