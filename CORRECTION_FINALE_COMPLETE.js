// CORRECTION FINALE COMPLÈTE - Résout tous les conflits de logique

console.log('🚀 CORRECTION FINALE COMPLÈTE - Démarrage...');

// Attendre que tous les objets soient chargés
function waitForObjects() {
    return new Promise((resolve) => {
        const checkObjects = () => {
            if (window.player && window.world && window.SmartLogger) {
                resolve();
            } else {
                setTimeout(checkObjects, 100);
            }
        };
        checkObjects();
    });
}

// Fonction principale de correction
async function applyCompleteCorrection() {
    await waitForObjects();
    
    console.log('✅ Objets détectés, application de la correction...');
    
    const player = window.player;
    const world = window.world;
    const smartLogger = window.SmartLogger;
    
    // 1. NETTOYER ET REMPLACER LA FONCTION UPDATE DU JOUEUR
    console.log('🔧 Remplacement de la fonction update du joueur...');
    
    // Sauvegarder les propriétés importantes
    const originalProperties = {
        eyeHeight: player.eyeHeight,
        playerHeight: player.playerHeight,
        collisionRadius: player.collisionRadius,
        autoClimb: player.autoClimb,
        autoClimbTrees: player.autoClimbTrees,
        stepHeight: player.stepHeight,
        stepCooldown: player.stepCooldown
    };
    
    // Nouvelle fonction update ultra-simplifiée et robuste
    player.update = function(delta, world) {
        // Validation du delta
        if (isNaN(delta) || delta <= 0) {
            delta = 0.016;
        }
        
        // Logs intelligents (réduits)
        if (window.SmartLogger && (this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1)) {
            window.SmartLogger.debug('PHYSICS', 'Mise à jour joueur', {
                position: {
                    x: Math.round(this.camera.position.x * 100) / 100,
                    y: Math.round(this.camera.position.y * 100) / 100,
                    z: Math.round(this.camera.position.z * 100) / 100
                },
                velocity: { y: Math.round(this.velocity.y * 1000) / 1000 },
                onGround: this.onGround,
                needsGroundCheck: this.needsGroundCheck
            });
        }
        
        // Positionnement initial
        if (this.needsGroundCheck && world && !this.flyMode) {
            const positioningSuccess = this.findGroundPosition(world);
            if (positioningSuccess) {
                this.needsGroundCheck = false;
                this.onGround = true;
                this.velocity.y = 0;
                console.log('✅ Positionnement initial réussi');
            }
            return; // Sortir tôt pendant le positionnement initial
        }
        
        // SYSTÈME DE PHYSIQUE UNIFIÉ ET SIMPLIFIÉ
        if (!this.flyMode && world) {
            const playerPos = this.camera.position;
            const feetY = playerPos.y - this.eyeHeight;
            
            // Détection du sol robuste
            let groundHeight = this.findGroundHeightRobust(world, playerPos.x, playerPos.z);
            
            if (groundHeight !== null && groundHeight !== undefined) {
                const targetY = groundHeight + 1 + this.eyeHeight;
                const distanceToGround = Math.abs(playerPos.y - targetY);
                
                // Maintenir le joueur au sol avec tolérance
                if (distanceToGround > 0.5) {
                    // Trop loin du sol - ajuster progressivement
                    const adjustSpeed = Math.min(distanceToGround * 10, 30) * delta;
                    if (playerPos.y > targetY) {
                        this.camera.position.y -= adjustSpeed;
                        this.velocity.y = -adjustSpeed / delta;
                    } else {
                        this.camera.position.y += adjustSpeed;
                        this.velocity.y = adjustSpeed / delta;
                    }
                    this.onGround = false;
                } else {
                    // Proche du sol - stabiliser
                    this.camera.position.y = targetY;
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                    
                    // Sauvegarder position stable
                    this.lastStablePosition = {
                        x: playerPos.x,
                        y: targetY,
                        z: playerPos.z
                    };
                }
            } else {
                // Pas de sol détecté - gravité avec protection
                this.onGround = false;
                this.velocity.y -= 30 * delta;
                if (this.velocity.y < -30) this.velocity.y = -30;
                
                // Protection contre chute infinie
                this.fallTime = (this.fallTime || 0) + delta * 1000;
                if (this.fallTime > 2000) {
                    // Repositionnement d'urgence
                    if (this.lastStablePosition && this.lastStablePosition.y > 0) {
                        this.camera.position.copy(this.lastStablePosition);
                    } else {
                        this.camera.position.set(0, 70, 0);
                    }
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                    console.warn('⚠️ Repositionnement d\'urgence effectué');
                }
            }
        }
        
        // Mouvement horizontal
        const horizontalMovement = new THREE.Vector3(
            this.velocity.x * delta,
            0,
            this.velocity.z * delta
        );
        this.camera.position.add(horizontalMovement);
        
        // Mouvement vertical (seulement si pas au sol)
        if (!this.onGround || this.flyMode) {
            this.camera.position.y += this.velocity.y * delta;
        }
        
        // Friction
        if (this.onGround) {
            this.velocity.x *= 0.9;
            this.velocity.z *= 0.9;
        }
        
        // Limiter la vitesse
        const maxSpeed = 20;
        const currentSpeed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
        if (currentSpeed > maxSpeed) {
            const scale = maxSpeed / currentSpeed;
            this.velocity.x *= scale;
            this.velocity.z *= scale;
        }
        
        // Mettre à jour le système de minage
        if (typeof this.updateMining === 'function') {
            this.updateMining(delta);
        }
    };
    
    // 2. AJOUTER UNE MÉTHODE DE DÉTECTION DU SOL ROBUSTE
    player.findGroundHeightRobust = function(world, x, z) {
        // Essayer d'abord la méthode standard
        let groundHeight = world.getGroundHeightAt(x, z);
        
        if (groundHeight !== null && groundHeight !== undefined) {
            return groundHeight;
        }
        
        // Méthode de fallback - recherche manuelle
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const chunkKey = `${chunkX},${chunkZ}`;
        
        if (world.chunks.has(chunkKey)) {
            const chunk = world.chunks.get(chunkKey).chunk;
            if (chunk && chunk.getBlockAt) {
                const localX = Math.max(0, Math.min(15, Math.floor(x) - chunkX * 16));
                const localZ = Math.max(0, Math.min(15, Math.floor(z) - chunkZ * 16));
                
                // Chercher le sol de haut en bas
                for (let y = 127; y >= 0; y--) {
                    try {
                        const blockType = chunk.getBlockAt(localX, y, localZ);
                        if (blockType && blockType !== 0) {
                            return y;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        }
        
        return null;
    };
    
    // 3. OPTIMISER LA FONCTION getGroundHeightAt DU MONDE
    console.log('🔧 Optimisation de la fonction getGroundHeightAt...');
    
    const originalGetGroundHeightAt = world.getGroundHeightAt;
    world.getGroundHeightAt = function(x, z) {
        // Cache simple et efficace
        const cacheKey = `${Math.floor(x)},${Math.floor(z)}`;
        const now = Date.now();
        
        if (!this.groundHeightCache) this.groundHeightCache = {};
        
        if (this.groundHeightCache[cacheKey]) {
            const cached = this.groundHeightCache[cacheKey];
            if (now - cached.time < 2000) { // Cache valide 2 secondes
                return cached.height;
            }
        }
        
        // Appeler la fonction originale
        const result = originalGetGroundHeightAt.call(this, x, z);
        
        // Mettre en cache si valide
        if (result !== null && result !== undefined) {
            this.groundHeightCache[cacheKey] = { height: result, time: now };
        }
        
        return result;
    };
    
    // 4. FORCER LE JOUEUR AU SOL IMMÉDIATEMENT
    console.log('🔧 Positionnement immédiat du joueur...');
    
    const currentPos = player.camera.position;
    const groundHeight = player.findGroundHeightRobust(world, currentPos.x, currentPos.z);
    
    if (groundHeight !== null) {
        const targetY = groundHeight + 1 + player.eyeHeight;
        player.camera.position.y = targetY;
        player.velocity.y = 0;
        player.onGround = true;
        player.needsGroundCheck = false;
        player.fallTime = 0;
        
        player.lastStablePosition = {
            x: currentPos.x,
            y: targetY,
            z: currentPos.z
        };
        
        console.log(`✅ Joueur positionné au sol: Y=${targetY.toFixed(2)}, Sol=${groundHeight}`);
    }
    
    // 5. SYSTÈME DE MONITORING INTELLIGENT
    console.log('📊 Activation du monitoring intelligent...');
    
    if (window.physicsMonitor) {
        clearInterval(window.physicsMonitor);
    }
    
    window.physicsMonitor = setInterval(() => {
        if (window.player && window.SmartLogger) {
            const pos = window.player.camera.position;
            const vel = window.player.velocity;
            const onGround = window.player.onGround;
            
            // Vérifier les problèmes
            if (!onGround && vel.y < -5) {
                window.SmartLogger.warn('PHYSICS', 'Chute rapide détectée', {
                    position: { x: pos.x.toFixed(1), y: pos.y.toFixed(1), z: pos.z.toFixed(1) },
                    velocity: vel.y.toFixed(2)
                });
                
                // Auto-correction
                const groundHeight = window.player.findGroundHeightRobust(window.world, pos.x, pos.z);
                if (groundHeight !== null) {
                    window.player.camera.position.y = groundHeight + 1 + window.player.eyeHeight;
                    window.player.velocity.y = 0;
                    window.player.onGround = true;
                    console.log('🔧 Auto-correction appliquée');
                }
            }
            
            // Log de statut périodique (réduit)
            if (!onGround || Math.abs(vel.y) > 0.1) {
                window.SmartLogger.info('PHYSICS', 'Statut joueur', {
                    position: { y: pos.y.toFixed(1) },
                    onGround: onGround,
                    velocityY: vel.y.toFixed(2)
                });
            }
        }
    }, 3000); // Vérification toutes les 3 secondes
    
    // 6. GÉNÉRER UN RAPPORT POUR L'IA
    setTimeout(() => {
        if (window.SmartLogger) {
            const report = window.SmartLogger.exportForAI();
            console.log('📊 RAPPORT IA GÉNÉRÉ:');
            console.log(report.reportText);
            
            // Sauvegarder le rapport
            localStorage.setItem('aiReport', JSON.stringify(report.report));
            localStorage.setItem('aiReportText', report.reportText);
        }
    }, 5000);
    
    console.log('🎉 CORRECTION FINALE COMPLÈTE APPLIQUÉE AVEC SUCCÈS !');
    console.log('📝 Le système est maintenant unifié et optimisé');
    
    return true;
}

// Fonction d'export du rapport IA
window.exportAIReport = function() {
    if (window.SmartLogger) {
        const report = window.SmartLogger.exportForAI();
        const blob = new Blob([report.reportText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-report-${new Date().toISOString().slice(0, 19)}.txt`;
        a.click();
        URL.revokeObjectURL(url);
        console.log('📄 Rapport IA téléchargé');
    }
};

// Fonction de vérification du statut
window.checkPhysicsStatus = function() {
    if (window.player) {
        const pos = window.player.camera.position;
        const vel = window.player.velocity;
        const onGround = window.player.onGround;
        
        console.log('📊 STATUT PHYSIQUE:');
        console.log(`   Position: (${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)})`);
        console.log(`   Vélocité Y: ${vel.y.toFixed(3)}`);
        console.log(`   Au sol: ${onGround}`);
        console.log(`   Chute: ${window.player.fallTime || 0}ms`);
        
        return { position: pos, velocity: vel, onGround: onGround };
    }
};

// Exécution automatique
applyCompleteCorrection().then(() => {
    console.log('✅ Correction complète terminée');
    
    // Vérification après 2 secondes
    setTimeout(() => {
        window.checkPhysicsStatus();
    }, 2000);
}).catch(error => {
    console.error('❌ Erreur lors de la correction:', error);
});

// Exporter les fonctions utiles
window.applyCompleteCorrection = applyCompleteCorrection;
window.checkPhysicsStatus = checkPhysicsStatus;
window.exportAIReport = exportAIReport;