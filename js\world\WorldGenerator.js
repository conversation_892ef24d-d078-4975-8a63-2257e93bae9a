// Classe pour générer le terrain du monde
export class WorldGenerator {
    constructor(seed = Math.random() * 10000) {
        this.seed = seed;
        
        // Générateurs de bruit pour différentes couches de terrain
        this.baseNoise = new SimplexNoise(seed);
        this.detailNoise = new SimplexNoise(seed + 1000);
        this.biomeNoise = new SimplexNoise(seed + 2000);
        this.mountainNoise = new SimplexNoise(seed + 3000);
        this.caveNoise = new SimplexNoise(seed + 4000);
        this.oreNoise = new SimplexNoise(seed + 5000);
        
        // Paramètres de terrain améliorés
        this.baseAmplitude = 25;   // Augmenté pour plus de relief
        this.detailAmplitude = 4;  // Plus de variation
        this.mountainAmplitude = 40; // Pour les zones montagneuses
        this.waterLevel = 60;      // Niveau d'eau fixe
        
        // Échelles de bruit
        this.baseScale = 0.004;    // Terrain de base
        this.detailScale = 0.025;  // Détails fins
        this.biomeScale = 0.0015;  // Biomes plus larges
        this.mountainScale = 0.002; // Montagnes
        this.caveScale = 0.05;     // Grottes
        this.oreScale = 0.1;       // Minerais
        
        // Définition des biomes
        this.biomes = {
            OCEAN: { min: 0.0, max: 0.15, name: 'Ocean' },
            BEACH: { min: 0.15, max: 0.25, name: 'Beach' },
            PLAINS: { min: 0.25, max: 0.45, name: 'Plains' },
            FOREST: { min: 0.45, max: 0.65, name: 'Forest' },
            HILLS: { min: 0.65, max: 0.8, name: 'Hills' },
            MOUNTAINS: { min: 0.8, max: 1.0, name: 'Mountains' }
        };
    }

    // Hauteur de base du terrain - Calcul amélioré avec biomes
    getHeight(x, z) {
        const biomeValue = this.getBiomeValue(x, z);
        const biome = this.getBiome(biomeValue);
        
        // Bruit de base
        const baseNoise = this.baseNoise.noise2D(x * this.baseScale, z * this.baseScale);
        
        // Modifier la hauteur selon le biome
        let baseHeight = this.waterLevel;
        let amplitude = this.baseAmplitude;
        
        switch (biome.name) {
            case 'Ocean':
                baseHeight = this.waterLevel - 15;
                amplitude = 8;
                break;
            case 'Beach':
                baseHeight = this.waterLevel - 2;
                amplitude = 3;
                break;
            case 'Plains':
                baseHeight = this.waterLevel + 5;
                amplitude = 12;
                break;
            case 'Forest':
                baseHeight = this.waterLevel + 8;
                amplitude = 18;
                break;
            case 'Hills':
                baseHeight = this.waterLevel + 15;
                amplitude = 25;
                break;
            case 'Mountains':
                baseHeight = this.waterLevel + 25;
                amplitude = this.mountainAmplitude;
                // Ajouter du bruit de montagne pour plus de relief
                const mountainNoise = this.mountainNoise.noise2D(x * this.mountainScale, z * this.mountainScale);
                baseHeight += mountainNoise * 20;
                break;
        }
        
        const finalHeight = baseHeight + (baseNoise * amplitude);
        return Math.floor(Math.max(10, finalHeight)); // Hauteur minimum de 10
    }
    
    // Variations de détail pour le terrain
    getDetailNoise(x, z) {
        return this.detailNoise.noise2D(x * this.detailScale, z * this.detailScale) * this.detailAmplitude;
    }
    
    // Valeur de biome (0-1) pour déterminer le type de terrain
    getBiomeValue(x, z) {
        return (this.biomeNoise.noise2D(x * this.biomeScale, z * this.biomeScale) + 1) * 0.5;
    }
    
    // Obtenir le biome à partir d'une valeur
    getBiome(biomeValue) {
        for (const biome of Object.values(this.biomes)) {
            if (biomeValue >= biome.min && biomeValue < biome.max) {
                return biome;
            }
        }
        return this.biomes.PLAINS; // Biome par défaut
    }
    
    // Vérifier si une position devrait avoir une grotte
    shouldHaveCave(x, y, z) {
        // Pas de grottes trop près de la surface ou trop profond
        if (y > 35 || y < 10) return false; // Zone réduite : 10-35 au lieu de 8-45

        // Vérifier la hauteur du terrain à cette position pour éviter les trous en surface
        const terrainHeight = this.getHeight(x, z);
        if (y > terrainHeight - 8) return false; // Minimum 8 blocs sous la surface

        const caveNoise = this.caveNoise.noise3D(x * this.caveScale, y * this.caveScale * 2, z * this.caveScale);
        return caveNoise > 0.7; // Seuil encore plus élevé : 0.7 au lieu de 0.65
    }
    
    // Obtenir le type de minerai à une position donnée
    getOreType(x, y, z) {
        if (y > 40) return null; // Pas de minerais près de la surface
        
        const oreNoise = this.oreNoise.noise3D(x * this.oreScale, y * this.oreScale, z * this.oreScale);
        
        // Distribution des minerais par profondeur
        if (y < 12) {
            // Zone profonde - diamant et or
            if (oreNoise > 0.85) return 'DIAMOND_ORE';
            if (oreNoise > 0.75) return 'GOLD_ORE';
        }
        
        if (y < 25) {
            // Zone moyenne - fer et or
            if (oreNoise > 0.8) return 'IRON_ORE';
            if (oreNoise > 0.7) return 'GOLD_ORE';
        }
        
        if (y < 40) {
            // Zone haute - charbon et fer
            if (oreNoise > 0.75) return 'COAL_ORE';
            if (oreNoise > 0.85) return 'IRON_ORE';
        }
        
        return null;
    }
    
    // Déterminer le type d'arbre selon le biome
    getTreeType(biomeValue) {
        const biome = this.getBiome(biomeValue);
        
        switch (biome.name) {
            case 'Forest':
                return Math.random() < 0.7 ? 'OAK' : 'BIRCH';
            case 'Hills':
                return Math.random() < 0.8 ? 'PINE' : 'OAK';
            case 'Mountains':
                return 'PINE';
            default:
                return 'OAK';
        }
    }
}