// CORRECTION SPÉCIFIQUE - Saut et Collision au Sol

console.log('🔧 CORRECTION SAUT ET COLLISION - Démarrage...');

// Attendre que les objets soient disponibles
function waitForObjects() {
    return new Promise((resolve) => {
        const checkObjects = () => {
            if (window.player && window.world) {
                resolve();
            } else {
                setTimeout(checkObjects, 100);
            }
        };
        checkObjects();
    });
}

async function applyJumpAndCollisionFix() {
    await waitForObjects();
    
    console.log('✅ Application de la correction saut et collision...');
    
    const player = window.player;
    const world = window.world;
    
    // 1. CORRIGER LE SYSTÈME DE SAUT
    console.log('🔧 Correction du système de saut...');
    
    // Variables pour contrôler le saut
    player.jumpCooldown = 0;
    player.jumpCooldownTime = 200; // 200ms entre les sauts
    player.isJumping = false;
    player.jumpStartTime = 0;
    player.maxJumpTime = 300; // Durée max d'un saut
    
    // Sauvegarder la fonction de saut originale si elle existe
    const originalJump = player.jump;
    
    // Nouvelle fonction de saut avec contrôle
    player.jump = function() {
        const now = Date.now();
        
        // Vérifier le cooldown
        if (now - this.jumpCooldown < this.jumpCooldownTime) {
            return false; // Saut bloqué par cooldown
        }
        
        // Vérifier si on est au sol
        if (!this.onGround) {
            return false; // Pas de double saut
        }
        
        // Vérifier si on est déjà en train de sauter
        if (this.isJumping && now - this.jumpStartTime < this.maxJumpTime) {
            return false; // Déjà en train de sauter
        }
        
        // Effectuer le saut
        this.velocity.y = 15;
        this.onGround = false;
        this.isJumping = true;
        this.jumpStartTime = now;
        this.jumpCooldown = now;
        
        // Log du saut (réduit)
        if (window.SmartLogger) {
            window.SmartLogger.debug('PHYSICS', 'Saut effectué', {
                velocityY: this.velocity.y,
                wasOnGround: true,
                flyMode: this.flyMode
            });
        }
        
        console.log(`🦘 Saut effectué - Cooldown: ${this.jumpCooldownTime}ms`);
        return true;
    };
    
    // 2. AMÉLIORER LE SYSTÈME DE COLLISION AU SOL
    console.log('🔧 Amélioration de la collision au sol...');
    
    // Remplacer la fonction update avec une physique plus naturelle
    const originalUpdate = player.update;
    player.update = function(delta, world) {
        // Validation du delta
        if (isNaN(delta) || delta <= 0) {
            delta = 0.016;
        }
        
        // Positionnement initial
        if (this.needsGroundCheck && world && !this.flyMode) {
            const positioningSuccess = this.findGroundPosition(world);
            if (positioningSuccess) {
                this.needsGroundCheck = false;
                this.onGround = true;
                this.velocity.y = 0;
                console.log('✅ Positionnement initial réussi');
            }
            return;
        }
        
        // PHYSIQUE NATURELLE AMÉLIORÉE
        if (!this.flyMode && world) {
            const playerPos = this.camera.position;
            const feetY = playerPos.y - this.eyeHeight;
            
            // Détection du sol
            let groundHeight = this.findGroundHeightRobust ? 
                this.findGroundHeightRobust(world, playerPos.x, playerPos.z) :
                world.getGroundHeightAt(playerPos.x, playerPos.z);
            
            if (groundHeight !== null && groundHeight !== undefined) {
                const targetY = groundHeight + 1 + this.eyeHeight;
                const distanceToGround = feetY - (groundHeight + 1);
                
                // LOGIQUE DE COLLISION NATURELLE
                if (distanceToGround <= 0.1 && this.velocity.y <= 0) {
                    // Atterrissage - poser au sol
                    this.camera.position.y = targetY;
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.isJumping = false;
                    this.fallTime = 0;
                    
                    // Sauvegarder position stable
                    this.lastStablePosition = {
                        x: playerPos.x,
                        y: targetY,
                        z: playerPos.z
                    };
                } else if (distanceToGround > 0.1) {
                    // En l'air - appliquer gravité
                    this.onGround = false;
                    
                    // Gravité normale
                    if (this.velocity.y > -30) {
                        this.velocity.y -= 30 * delta;
                    }
                    
                    // Protection contre chute infinie
                    this.fallTime = (this.fallTime || 0) + delta * 1000;
                    if (this.fallTime > 3000) {
                        // Repositionnement d'urgence après 3 secondes
                        this.camera.position.y = targetY;
                        this.velocity.y = 0;
                        this.onGround = true;
                        this.fallTime = 0;
                        console.warn('⚠️ Repositionnement d\'urgence - chute trop longue');
                    }
                } else {
                    // Très proche du sol mais pas tout à fait - laisser la physique normale
                    this.onGround = false;
                }
            } else {
                // Pas de sol détecté - gravité avec protection
                this.onGround = false;
                this.velocity.y -= 30 * delta;
                if (this.velocity.y < -30) this.velocity.y = -30;
                
                this.fallTime = (this.fallTime || 0) + delta * 1000;
                if (this.fallTime > 2000) {
                    // Position de secours
                    this.camera.position.set(0, 70, 0);
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                }
            }
        }
        
        // Mouvement horizontal
        const horizontalMovement = new THREE.Vector3(
            this.velocity.x * delta,
            0,
            this.velocity.z * delta
        );
        this.camera.position.add(horizontalMovement);
        
        // Mouvement vertical
        this.camera.position.y += this.velocity.y * delta;
        
        // Friction
        if (this.onGround) {
            this.velocity.x *= 0.9;
            this.velocity.z *= 0.9;
        }
        
        // Limiter la vitesse
        const maxSpeed = 20;
        const currentSpeed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
        if (currentSpeed > maxSpeed) {
            const scale = maxSpeed / currentSpeed;
            this.velocity.x *= scale;
            this.velocity.z *= scale;
        }
        
        // Mettre à jour le système de minage
        if (typeof this.updateMining === 'function') {
            this.updateMining(delta);
        }
    };
    
    // 3. CORRIGER LA GESTION DES TOUCHES
    console.log('🔧 Correction de la gestion des touches...');
    
    // Trouver et corriger les event listeners de saut
    const canvas = document.getElementById('game');
    if (canvas) {
        // Supprimer les anciens listeners s'ils existent
        canvas.removeEventListener('keydown', window.jumpKeyHandler);
        document.removeEventListener('keydown', window.jumpKeyHandler);
        
        // Nouveau gestionnaire de touches pour le saut
        window.jumpKeyHandler = function(event) {
            if (event.code === 'Space' && !event.repeat) {
                event.preventDefault();
                if (window.player && typeof window.player.jump === 'function') {
                    window.player.jump();
                }
            }
        };
        
        // Ajouter le nouveau listener
        document.addEventListener('keydown', window.jumpKeyHandler);
        console.log('✅ Gestionnaire de saut corrigé');
    }
    
    // 4. OPTIMISER LES PERFORMANCES
    console.log('🔧 Optimisation des performances...');
    
    // Réduire la fréquence des logs de saut
    let lastJumpLog = 0;
    const originalLog = console.log;
    
    // Intercepter les logs de saut répétitifs
    if (window.SmartLogger) {
        const originalDebug = window.SmartLogger.debug;
        window.SmartLogger.debug = function(category, message, data) {
            if (category === 'PHYSICS' && message === 'Saut effectué') {
                const now = Date.now();
                if (now - lastJumpLog < 1000) {
                    return; // Ignorer les logs de saut trop fréquents
                }
                lastJumpLog = now;
            }
            return originalDebug.call(this, category, message, data);
        };
    }
    
    // 5. MONITORING ET DIAGNOSTIC
    console.log('📊 Activation du monitoring...');
    
    if (window.jumpMonitor) {
        clearInterval(window.jumpMonitor);
    }
    
    window.jumpMonitor = setInterval(() => {
        if (window.player) {
            const pos = window.player.camera.position;
            const vel = window.player.velocity;
            const onGround = window.player.onGround;
            const isJumping = window.player.isJumping;
            
            // Détecter les problèmes de saut
            if (isJumping && Date.now() - window.player.jumpStartTime > window.player.maxJumpTime) {
                window.player.isJumping = false;
                console.log('🔧 Saut terminé automatiquement');
            }
            
            // Log de statut (réduit)
            if (!onGround || Math.abs(vel.y) > 1) {
                console.log(`📊 Physique: Y=${pos.y.toFixed(1)} VelY=${vel.y.toFixed(1)} Sol=${onGround} Saut=${isJumping}`);
            }
        }
    }, 2000);
    
    console.log('🎉 CORRECTION SAUT ET COLLISION APPLIQUÉE !');
    console.log('📝 Le saut devrait maintenant être contrôlé et naturel');
    
    return true;
}

// Fonction de test du saut
window.testJump = function() {
    if (window.player && typeof window.player.jump === 'function') {
        const result = window.player.jump();
        console.log(`🧪 Test de saut: ${result ? 'Réussi' : 'Échoué'}`);
        return result;
    }
    return false;
};

// Fonction de diagnostic
window.checkJumpStatus = function() {
    if (window.player) {
        const now = Date.now();
        console.log('📊 STATUT DU SAUT:');
        console.log(`   Au sol: ${window.player.onGround}`);
        console.log(`   En train de sauter: ${window.player.isJumping}`);
        console.log(`   Cooldown restant: ${Math.max(0, window.player.jumpCooldownTime - (now - window.player.jumpCooldown))}ms`);
        console.log(`   Vélocité Y: ${window.player.velocity.y.toFixed(2)}`);
        
        return {
            onGround: window.player.onGround,
            isJumping: window.player.isJumping,
            velocityY: window.player.velocity.y,
            cooldownRemaining: Math.max(0, window.player.jumpCooldownTime - (now - window.player.jumpCooldown))
        };
    }
};

// Exécution automatique
applyJumpAndCollisionFix().then(() => {
    console.log('✅ Correction saut et collision terminée');
    
    // Test après 2 secondes
    setTimeout(() => {
        window.checkJumpStatus();
    }, 2000);
}).catch(error => {
    console.error('❌ Erreur lors de la correction:', error);
});

// Exporter les fonctions
window.applyJumpAndCollisionFix = applyJumpAndCollisionFix;
window.testJump = testJump;
window.checkJumpStatus = checkJumpStatus;