// CORRECTION FINALE - Système de physique forcé pour résoudre le problème de chute

// Cette correction force le joueur à rester au sol en remplaçant complètement
// le système de détection du sol défaillant par une version ultra-simplifiée

function applyFinalPhysicsFix() {
    console.log('🔧 APPLICATION DE LA CORRECTION FINALE DE PHYSIQUE');
    
    if (!window.player || !window.world) {
        console.error('❌ Objets player ou world non trouvés');
        return false;
    }
    
    const player = window.player;
    const world = window.world;
    
    // 1. FORCER LE JOUEUR AU SOL IMMÉDIATEMENT
    const currentPos = player.camera.position;
    
    // Trouver un chunk disponible proche
    const availableChunks = Array.from(world.chunks.keys());
    if (availableChunks.length > 0) {
        // Utiliser le chunk le plus proche de la position actuelle
        let bestChunk = null;
        let bestDistance = Infinity;
        
        for (const chunkKey of availableChunks) {
            const [chunkX, chunkZ] = chunkKey.split(',').map(Number);
            const chunkCenterX = chunkX * 16 + 8;
            const chunkCenterZ = chunkZ * 16 + 8;
            const distance = Math.sqrt(
                Math.pow(chunkCenterX - currentPos.x, 2) + 
                Math.pow(chunkCenterZ - currentPos.z, 2)
            );
            
            if (distance < bestDistance) {
                bestDistance = distance;
                bestChunk = chunkKey;
            }
        }
        
        if (bestChunk) {
            const [chunkX, chunkZ] = bestChunk.split(',').map(Number);
            const chunk = world.chunks.get(bestChunk);
            
            if (chunk && chunk.chunk) {
                // Positionner au centre du chunk
                const spawnX = chunkX * 16 + 8;
                const spawnZ = chunkZ * 16 + 8;
                
                // Chercher le sol dans ce chunk
                let groundY = 70; // Valeur par défaut
                
                try {
                    for (let y = 127; y >= 1; y--) {
                        const blockType = chunk.chunk.getBlockAt(8, y, 8);
                        if (blockType && blockType !== 0) {
                            groundY = y;
                            break;
                        }
                    }
                } catch (e) {
                    console.warn('Erreur lors de la recherche du sol, utilisation valeur par défaut');
                }
                
                // FORCER LA POSITION
                player.camera.position.x = spawnX;
                player.camera.position.y = groundY + 1 + player.eyeHeight;
                player.camera.position.z = spawnZ;
                player.velocity.y = 0;
                player.onGround = true;
                player.needsGroundCheck = false;
                player.fallTime = 0;
                
                // Sauvegarder comme position stable
                player.lastStablePosition = {
                    x: spawnX,
                    y: player.camera.position.y,
                    z: spawnZ
                };
                
                console.log(`✅ Joueur forcé au sol à (${spawnX}, ${player.camera.position.y.toFixed(2)}, ${spawnZ})`);
                console.log(`📍 Chunk: ${bestChunk}, Sol Y: ${groundY}`);
            }
        }
    }
    
    // 2. REMPLACER LA FONCTION UPDATE AVEC UNE VERSION SIMPLIFIÉE
    const originalUpdate = player.update;
    player.update = function(delta, world) {
        // Vérifier que delta est valide
        if (isNaN(delta) || delta <= 0) {
            delta = 0.016;
        }
        
        // SYSTÈME DE PHYSIQUE ULTRA-SIMPLIFIÉ
        if (!this.flyMode && world) {
            const playerPos = this.camera.position;
            const feetY = playerPos.y - this.eyeHeight;
            
            // Déterminer le chunk actuel
            const chunkX = Math.floor(playerPos.x / 16);
            const chunkZ = Math.floor(playerPos.z / 16);
            const chunkKey = `${chunkX},${chunkZ}`;
            
            let groundFound = false;
            let groundHeight = 70; // Valeur par défaut
            
            // Essayer de trouver le sol
            if (world.chunks.has(chunkKey)) {
                const chunk = world.chunks.get(chunkKey).chunk;
                if (chunk && chunk.getBlockAt) {
                    const localX = Math.max(0, Math.min(15, Math.floor(playerPos.x) - chunkX * 16));
                    const localZ = Math.max(0, Math.min(15, Math.floor(playerPos.z) - chunkZ * 16));
                    
                    // Chercher le sol
                    for (let y = Math.min(127, Math.floor(feetY) + 10); y >= 0; y--) {
                        try {
                            const blockType = chunk.getBlockAt(localX, y, localZ);
                            if (blockType && blockType !== 0) {
                                groundHeight = y;
                                groundFound = true;
                                break;
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }
            }
            
            // FORCER LE JOUEUR AU SOL
            const targetY = groundHeight + 1 + this.eyeHeight;
            const distanceToGround = Math.abs(playerPos.y - targetY);
            
            if (distanceToGround > 0.5) {
                // Trop loin du sol - téléporter
                this.camera.position.y = targetY;
                this.velocity.y = 0;
                this.onGround = true;
                this.fallTime = 0;
            } else {
                // Proche du sol - maintenir
                this.camera.position.y = targetY;
                this.velocity.y = 0;
                this.onGround = true;
                this.fallTime = 0;
            }
            
            // Sauvegarder position stable
            this.lastStablePosition = {
                x: playerPos.x,
                y: this.camera.position.y,
                z: playerPos.z
            };
        }
        
        // Appliquer le mouvement horizontal seulement
        const movement = new THREE.Vector3(this.velocity.x * delta, 0, this.velocity.z * delta);
        this.camera.position.add(movement);
        
        // Friction
        if (this.onGround) {
            this.velocity.x *= 0.9;
            this.velocity.z *= 0.9;
        }
        
        // Limiter la vitesse
        const maxSpeed = 20;
        const currentSpeed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
        if (currentSpeed > maxSpeed) {
            const scale = maxSpeed / currentSpeed;
            this.velocity.x *= scale;
            this.velocity.z *= scale;
        }
        
        // Mettre à jour le système de minage
        if (typeof this.updateMining === 'function') {
            this.updateMining(delta);
        }
    };
    
    console.log('🎉 CORRECTION FINALE APPLIQUÉE AVEC SUCCÈS !');
    console.log('📝 Le joueur est maintenant forcé à rester au sol');
    
    return true;
}

// 3. CRÉER UN SYSTÈME DE MONITORING
function createPhysicsMonitor() {
    if (window.physicsMonitor) {
        clearInterval(window.physicsMonitor);
    }
    
    window.physicsMonitor = setInterval(() => {
        if (window.player) {
            const pos = window.player.camera.position;
            const vel = window.player.velocity;
            const onGround = window.player.onGround;
            
            // Vérifier si le joueur tombe
            if (!onGround && vel.y < -1) {
                console.warn(`⚠️ JOUEUR EN CHUTE DÉTECTÉ - Correction automatique`);
                applyFinalPhysicsFix();
            }
            
            // Log de statut (réduit)
            if (Math.abs(vel.y) > 0.1 || !onGround) {
                console.log(`📊 Statut: Pos(${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}) Vel(${vel.y.toFixed(2)}) Sol:${onGround}`);
            }
        }
    }, 2000); // Vérification toutes les 2 secondes
    
    console.log('📊 Monitoring de physique activé');
}

// EXÉCUTION AUTOMATIQUE
console.log('🚀 Chargement de la correction finale de physique...');

// Attendre que les objets soient disponibles
function waitAndApply() {
    if (window.player && window.world) {
        applyFinalPhysicsFix();
        createPhysicsMonitor();
    } else {
        console.log('⏳ Attente des objets player et world...');
        setTimeout(waitAndApply, 1000);
    }
}

waitAndApply();

// Exporter les fonctions pour utilisation manuelle
window.applyFinalPhysicsFix = applyFinalPhysicsFix;
window.createPhysicsMonitor = createPhysicsMonitor;