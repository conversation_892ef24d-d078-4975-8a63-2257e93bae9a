import { Chunk } from './Chunk.js';
import { WorldGenerator } from './WorldGenerator.js';
import { WorkerManager } from '../utils/WorkerManager.js';

export class World {
    constructor(scene, textureGenerator = null) {
        // Récupérer le logger global
        this.logger = window.GameLogger;
        
        // Intégrer le générateur de textures
        this.textureGenerator = textureGenerator;
        
        const WORLD_VERSION = `World-v${Date.now()}`;
        this.logger.info('Système de monde chargé', {
            version: WORLD_VERSION,
            features: {
                cacheBuster: 'Actif',
                rechercheOptimisee: 'Recherche de sol optimisée',
                logsDetailles: 'Activés dans getGroundHeightAt',
                desactivationRechercheSol: 'Implémentée'
            }
        });

        this.version = WORLD_VERSION;
        this.scene = scene;
        this.chunks = new Map();
        this.generator = new WorldGenerator();

        // Configuration des distances de rendu et de charge
        this.renderDistance = 6; // Distance réduite pour optimiser les performances
        this.loadDistance = 10; // Distance de chargement (peut être plus grande que la distance de rendu)

        // Position du joueur
        this.lastPlayerChunkX = 0;
        this.lastPlayerChunkZ = 0;

        // Gestion des chunks et de leur priorité
        this.generationQueue = [];
        this.maxChunksPerFrame = 2; // Réduit pour moins de pression sur le CPU
        this.workerManager = new WorkerManager();

        // Suivi des chunks en cours de génération pour éviter les doublons
        this.chunksBeingGenerated = new Set();

        // Cache de visibilité pour réduire les calculs
        this.visibilityCache = new Map();
        this.visibilityCacheTimeout = 500; // ms
        this.lastVisibilityUpdate = 0;

        // Cache pour éviter les appels répétitifs de getGroundHeightAt
        this.groundHeightCache = {};
        this.groundSearchDisabled = false;

        // Précharger les chunks autour du joueur
        this.preloadChunks(0, 0);

        // Générer immédiatement les chunks centraux
        this.generateInitialChunks();
        this.generateInitialChunks();

        // Système de désactivation de la recherche de sol pour éviter les boucles infinies
        this.groundSearchDisabled = false;
        this.cachedGroundHeight = 70; // Hauteur par défaut

        // Statistiques de performance
        this.stats = {
            chunksGenerated: 0,
            chunksRendered: 0,
            lastFrameTime: performance.now(),
            frameCount: 0
        };
    }

    update(playerX, playerZ) {
        // Calculer le temps delta pour les statistiques
        const now = performance.now();
        const delta = now - this.stats.lastFrameTime;
        this.stats.lastFrameTime = now;
        this.stats.frameCount++;

        // Calculer la position du chunk du joueur
        const chunkX = Math.floor(playerX / 16);
        const chunkZ = Math.floor(playerZ / 16);

        // Détecter si le joueur a changé de chunk
        const hasChangedChunk = (chunkX !== this.lastPlayerChunkX || chunkZ !== this.lastPlayerChunkZ);

        // Si le joueur a changé de chunk, mettre à jour la logique de chargement
        if (hasChangedChunk) {
            this.logger.chunk('Joueur déplacé vers nouveau chunk', {
                newChunk: { x: chunkX, z: chunkZ },
                oldChunk: { x: this.lastPlayerChunkX, z: this.lastPlayerChunkZ }
            });
            this.lastPlayerChunkX = chunkX;
            this.lastPlayerChunkZ = chunkZ;

            // Précharger les nouveaux chunks autour de la position actuelle
            this.preloadChunks(chunkX, chunkZ);

            // Réinitialiser le cache de visibilité
            this.visibilityCache.clear();

            // Forcer la mise à jour de la visibilité immédiatement
            this.updateVisibility(playerX, playerZ);
            this.lastVisibilityUpdate = now;
        }
        // Sinon, vérifier si une mise à jour périodique est nécessaire
        else if (now - this.lastVisibilityUpdate > this.visibilityCacheTimeout) {
            this.updateVisibility(playerX, playerZ);
            this.lastVisibilityUpdate = now;
        }

        // Vérifier si des chunks visibles manquent
        const missingVisibleChunks = this.checkForMissingVisibleChunks(chunkX, chunkZ);
        if (missingVisibleChunks > 0) {
            // Force une génération plus rapide si des chunks visibles manquent
            this.maxChunksPerFrame = Math.min(this.maxChunksPerFrame + 1, 5); // Augmenter progressivement
        } else {
            // Retour à la normale
            this.maxChunksPerFrame = 2;
        }

        // Générer quelques chunks par frame de manière asynchrone
        this.generateChunksInQueue();

        // Afficher les statistiques régulièrement
        if (this.stats.frameCount % 300 === 0) { // ~5 secondes à 60fps
            this.logger.debug('Statistiques de performance', {
                fps: Math.round(1000 / delta),
                chunksGenerated: this.stats.chunksGenerated,
                chunksRendered: this.stats.chunksRendered,
                chunksInQueue: this.generationQueue.length,
                chunksBeingGenerated: this.chunksBeingGenerated.size
            });
        }
    }

    preloadChunks(chunkX, chunkZ) {
        // Tableau pour stocker les chunks à charger avec leur priorité
        const chunksToLoad = [];

        // Parcourir tous les chunks dans la zone de chargement
        for (let x = -this.loadDistance; x <= this.loadDistance; x++) {
            for (let z = -this.loadDistance; z <= this.loadDistance; z++) {
                const targetX = chunkX + x;
                const targetZ = chunkZ + z;
                const key = `${targetX},${targetZ}`;

                // Ne pas ajouter les chunks déjà chargés
                if (!this.chunks.has(key)) {
                    // Calculer la distance pour la priorité
                    const distance = Math.sqrt(x * x + z * z);

                    // Ajouter à la liste de chargement avec priorité
                    chunksToLoad.push({
                        x: targetX,
                        z: targetZ,
                        key,
                        distance,
                        priority: distance <= this.renderDistance ? 1 : 2 // Priorité 1: visible, 2: non visible
                    });
                }
            }
        }

        // Trier par priorité puis par distance
        chunksToLoad.sort((a, b) => {
            if (a.priority !== b.priority) return a.priority - b.priority;
            return a.distance - b.distance;
        });

        // Fusionner avec la file d'attente existante (uniquement les nouveaux chunks)
        const existingKeys = new Set(this.generationQueue.map(chunk => chunk.key));
        const newChunks = chunksToLoad.filter(chunk => !existingKeys.has(chunk.key));

        // Limiter à 100 chunks max dans la file pour éviter la surcharge
        const maxQueueSize = 100;
        if (this.generationQueue.length + newChunks.length > maxQueueSize) {
            const spaceAvailable = Math.max(0, maxQueueSize - this.generationQueue.length);
            newChunks.splice(spaceAvailable); // Garder seulement les chunks qui tiennent dans la file
        }

        // Ajouter les nouveaux chunks à la file
        this.generationQueue = this.generationQueue.concat(newChunks);
    }

    async generateInitialChunks() {
        // Message de chargement
        this.logger.info('Génération des chunks initiaux...');

        // Définir les chunks centraux avec ordre de priorité (zone plus large)
        const centerChunks = [];

        // Générer une grille 5x5 de chunks autour du centre
        const radius = 2; // Rayon de 2 = grille 5x5
        for (let x = -radius; x <= radius; x++) {
            for (let z = -radius; z <= radius; z++) {
                // Ajouter le chunk à la liste avec une priorité basée sur la distance
                const distance = Math.sqrt(x * x + z * z);
                centerChunks.push({ x, z, priority: distance });
            }
        }

        // Trier les chunks par ordre de priorité (du plus proche au plus éloigné)
        centerChunks.sort((a, b) => a.priority - b.priority);

        // Traiter les chunks par lots pour éviter de bloquer le navigateur
        const batchSize = 5;
        
        this.logger.chunk('Préparation des chunks initiaux', {
            totalChunks: centerChunks.length,
            radius: radius,
            batchSize: batchSize
        });
        let processedCount = 0;

        while (processedCount < centerChunks.length) {
            const batch = centerChunks.slice(processedCount, processedCount + batchSize);
            processedCount += batchSize;

            // Générer les chunks de ce lot avec des promesses parallèles
            const promises = batch.map(async (chunkData) => {
                const key = `${chunkData.x},${chunkData.z}`;
                if (!this.chunks.has(key)) {
                    try {
                        // Utiliser le worker pour générer les données du chunk
                        const blocks = await this.workerManager.generateChunk(chunkData.x, chunkData.z);

                        // Créer le chunk avec les données du worker
                        const chunk = new Chunk(chunkData.x, chunkData.z, null);
                        chunk.blocks = blocks;

                        // Construire et ajouter le mesh
                        const mesh = chunk.buildMesh();
                        this.scene.add(mesh);
                        this.chunks.set(key, {
                            chunk,
                            mesh,
                            visible: true // Définir la visibilité initiale
                        });

                        this.stats.chunksGenerated++;
                        this.stats.chunksRendered++;

                        this.logger.chunk('Chunk généré avec succès', {
                            chunkKey: key,
                            position: { x: chunkData.x, z: chunkData.z },
                            blocksCount: blocks ? blocks.length : 0
                        });
                        return key;
                    } catch (error) {
                        this.logger.error('Erreur lors de la génération du chunk', {
                            chunkKey: key,
                            position: { x: chunkData.x, z: chunkData.z },
                            error: error.message,
                            stack: error.stack
                        });
                        return null;
                    }
                }
                return key;
            });

            // Attendre que ce lot soit terminé
            const results = await Promise.allSettled(promises);
            const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length;

            this.logger.chunk('Lot de chunks généré', {
                successCount: successCount,
                totalInBatch: batch.length,
                processedTotal: processedCount
            });

            // Petite pause pour permettre au navigateur de respirer
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        this.logger.info('Génération de chunks initiaux terminée', {
            totalGenerated: this.stats.chunksGenerated,
            totalRendered: this.stats.chunksRendered
        });
    }

    generateChunksInQueue() {
        // Limiter le nombre de chunks générés par frame
        const toGenerate = Math.min(this.generationQueue.length, this.maxChunksPerFrame);

        // Si aucun chunk à générer, sortir tôt
        if (toGenerate <= 0) return;

        for (let i = 0; i < toGenerate; i++) {
            const chunkData = this.generationQueue.shift();
            if (!chunkData) continue; // Protection contre les données null

            // Générer la clé de chunk si elle n'existe pas déjà
            const chunkKey = `${chunkData.x},${chunkData.z}`;

            // Vérifier si ce chunk a déjà été généré entre temps OU est en cours de génération
            if (this.chunks.has(chunkKey) || this.chunksBeingGenerated.has(chunkKey)) {
                continue;
            }

            this.logger.chunk('Début génération chunk asynchrone', {
                position: { x: chunkData.x, z: chunkData.z },
                priority: chunkData.priority,
                queueRemaining: this.generationQueue.length
            });

            // Marquer le chunk comme étant en cours de génération
            this.chunksBeingGenerated.add(chunkKey);

            // Utiliser le WorkerManager pour une génération asynchrone
            this.workerManager.generateChunk(chunkData.x, chunkData.z)
                .then(blocks => {
                    // Retirer le chunk de la liste des chunks en cours de génération
                    this.chunksBeingGenerated.delete(chunkKey);

                    // Vérifier à nouveau si le chunk existe déjà (peut avoir été créé pendant la génération)
                    if (this.chunks.has(chunkKey)) return;

                    this.logger.chunk('Données reçues du worker', {
                        position: { x: chunkData.x, z: chunkData.z },
                        blocksReceived: blocks ? blocks.length : 0
                    });

                    // Créer le chunk avec les données reçues du worker
                    const chunk = new Chunk(chunkData.x, chunkData.z, null);
                    chunk.blocks = blocks;

                    // Construire le mesh seulement si ce chunk est dans la distance de rendu
                    if (this.isChunkInRenderDistance(chunkData.x, chunkData.z)) {
                        const mesh = chunk.buildMesh();
                        this.scene.add(mesh);
                        this.chunks.set(chunkKey, { chunk, mesh, visible: true });
                        this.stats.chunksRendered++;
                        this.logger.chunk('Chunk ajouté à la scène', {
                            position: { x: chunkData.x, z: chunkData.z },
                            totalRendered: this.stats.chunksRendered,
                            visible: true
                        });
                    } else {
                        // Stocker le chunk sans mesh pour l'instant
                        this.chunks.set(chunkKey, { chunk, mesh: null, visible: false });
                        this.logger.chunk('Chunk stocké sans mesh', {
                            position: { x: chunkData.x, z: chunkData.z },
                            reason: 'Hors distance de rendu'
                        });
                    }

                    this.stats.chunksGenerated++;
                })
                .catch(error => {
                    // Retirer le chunk de la liste des chunks en cours de génération même en cas d'erreur
                    this.chunksBeingGenerated.delete(chunkKey);
                    this.logger.error('Erreur génération chunk asynchrone', {
                        position: { x: chunkData.x, z: chunkData.z },
                        error: error.message,
                        stack: error.stack
                    });
                    // Ne pas remettre dans la file pour éviter les boucles infinies
                    // Le chunk sera re-détecté comme manquant si nécessaire
                });
        }
    }

    // Méthode pour mettre à jour la visibilité des chunks
    updateVisibility(playerX, playerZ) {
        const playerChunkX = Math.floor(playerX / 16);
        const playerChunkZ = Math.floor(playerZ / 16);

        // Parcourir tous les chunks
        for (const [key, chunkData] of this.chunks.entries()) {
            const [x, z] = key.split(',').map(Number);

            // Calculer la distance en chunks
            const distance = Math.sqrt(
                Math.pow(x - playerChunkX, 2) +
                Math.pow(z - playerChunkZ, 2)
            );

            // Déterminer si le chunk doit être visible
            const shouldBeVisible = distance <= this.renderDistance;

            // Si l'état de visibilité doit changer
            if (shouldBeVisible !== chunkData.visible) {
                if (shouldBeVisible) {
                    // Rendre visible: créer et ajouter le mesh
                    if (!chunkData.mesh) {
                        const mesh = chunkData.chunk.buildMesh();
                        this.scene.add(mesh);
                        chunkData.mesh = mesh;
                        this.stats.chunksRendered++;
                    } else {
                        chunkData.mesh.visible = true;
                    }
                } else {
                    // Rendre invisible: cacher le mesh pour économiser les ressources
                    if (chunkData.mesh) {
                        chunkData.mesh.visible = false;
                    }
                }

                // Mettre à jour l'état de visibilité
                chunkData.visible = shouldBeVisible;
            }
        }
    }

    // Vérifier si un chunk est dans la distance de rendu
    isChunkInRenderDistance(x, z) {
        const distance = Math.sqrt(
            Math.pow(x - this.lastPlayerChunkX, 2) +
            Math.pow(z - this.lastPlayerChunkZ, 2)
        );
        return distance <= this.renderDistance;
    }

    // Vérifier les chunks visibles manquants qui devraient être générés
    checkForMissingVisibleChunks(playerChunkX, playerChunkZ) {
        let missingCount = 0;
        let newChunksAdded = 0;

        // Vérifier dans un rayon visible autour du joueur
        for (let x = playerChunkX - this.renderDistance; x <= playerChunkX + this.renderDistance; x++) {
            for (let z = playerChunkZ - this.renderDistance; z <= playerChunkZ + this.renderDistance; z++) {
                // Calculer la distance
                const distance = Math.sqrt(
                    Math.pow(x - playerChunkX, 2) +
                    Math.pow(z - playerChunkZ, 2)
                );

                // Si ce chunk est dans la distance de rendu mais n'existe pas, l'ajouter à la file
                if (distance <= this.renderDistance) {
                    const key = `${x},${z}`;
                    if (!this.chunks.has(key)) {
                        // Vérifier si ce chunk est déjà dans la file d'attente OU en cours de génération
                        const isInQueue = this.generationQueue.some(item => item.x === x && item.z === z);
                        const isBeingGenerated = this.chunksBeingGenerated.has(key);

                        if (!isInQueue && !isBeingGenerated) {
                            missingCount++;
                            newChunksAdded++;

                            // Ajouter avec haute priorité (valeur faible = haute priorité)
                            this.generationQueue.push({
                                x, z, key, priority: distance * 0.5
                            });
                            console.log(`Chunk manquant détecté: (${x}, ${z}), distance: ${distance.toFixed(2)}`);
                        }
                    }
                }
            }
        }

        // Trier la file d'attente par priorité seulement si on a ajouté de nouveaux chunks
        if (newChunksAdded > 0) {
            this.generationQueue.sort((a, b) => a.priority - b.priority);
            console.log(`${newChunksAdded} chunks manquants détectés et ajoutés à la file`);
        }

        return missingCount;
    }

    // Méthode pour vérifier les collisions avec le monde (version optimisée et stable)
    hasCollisionAt(x, y, z, radius = 0.3) {
        // Déterminer dans quel chunk se trouve la position
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;

        // Si le chunk n'existe pas, pas de collision
        if (!this.chunks.has(key)) {
            return false;
        }

        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;

        // Vérifier que le chunk a des blocs
        if (!chunk || !chunk.blocks) {
            return false;
        }

        // Calculer les coordonnées locales dans le chunk (correction pour les valeurs négatives)
        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;
        const localY = Math.floor(y);

        // S'assurer que les coordonnées locales sont dans les limites
        localX = Math.max(0, Math.min(15, localX));
        localZ = Math.max(0, Math.min(15, localZ));

        // Vérification simplifiée - seulement le bloc central et immédiatement adjacent
        for (let offsetY = 0; offsetY <= 1; offsetY++) {
            const checkY = localY + offsetY;
            if (checkY < 0 || checkY >= 128) continue;

            for (let offsetX = -1; offsetX <= 1; offsetX++) {
                for (let offsetZ = -1; offsetZ <= 1; offsetZ++) {
                    const checkX = localX + offsetX;
                    const checkZ = localZ + offsetZ;

                    // Si en dehors du chunk, ignorer
                    if (checkX < 0 || checkX >= 16 || checkZ < 0 || checkZ >= 16) continue;

                    // Vérifier si le bloc existe
                    const blockType = chunk.getBlockAt(checkX, checkY, checkZ);
                    if (blockType !== 0) { // 0 = air
                        // Distance entre le centre du joueur et le bloc
                        const blockWorldX = chunkX * 16 + checkX;
                        const blockWorldZ = chunkZ * 16 + checkZ;

                        const dx = Math.abs(x - (blockWorldX + 0.5));
                        const dy = Math.abs(y - (checkY + 0.5));
                        const dz = Math.abs(z - (blockWorldZ + 0.5));

                        // Collision en boîte plutôt qu'en sphère pour plus de stabilité
                        if (dx < radius + 0.5 && dy < radius + 0.5 && dz < radius + 0.5) {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    // Méthode spécialisée pour trouver le sol (version simplifiée et optimisée)
    getGroundHeightAt(x, z) {
        // Cache simple pour éviter les recalculs
        const cacheKey = `${Math.floor(x)},${Math.floor(z)}`;
        const now = Date.now();
        
        if (this.groundHeightCache && this.groundHeightCache[cacheKey]) {
            const cached = this.groundHeightCache[cacheKey];
            // Cache valide pendant 1 seconde
            if (now - cached.time < 1000) {
                return cached.height;
            }
        }

        // Déterminer dans quel chunk se trouve la position
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;

        // Si le chunk n'existe pas, retourner null
        if (!this.chunks.has(key)) {
            return null;
        }

        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;

        // Vérifier que le chunk a des blocs
        if (!chunk || !chunk.blocks || typeof chunk.getBlockAt !== 'function') {
            return null;
        }

        // Calculer les coordonnées locales dans le chunk
        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;

        // S'assurer que les coordonnées locales sont dans les limites
        localX = Math.max(0, Math.min(15, localX));
        localZ = Math.max(0, Math.min(15, localZ));

        // Chercher le bloc le plus haut qui n'est pas de l'air
        for (let y = 127; y >= 0; y--) {
            try {
                const blockType = chunk.getBlockAt(localX, y, localZ);
                if (blockType && blockType !== 0) { // 0 = air
                    // Sauvegarder dans le cache
                    if (!this.groundHeightCache) this.groundHeightCache = {};
                    this.groundHeightCache[cacheKey] = { height: y, time: now };
                    
                    return y;
                }
            } catch (error) {
                // Erreur lors de l'accès au bloc - continuer la recherche
                continue;
            }
        }

        if (!this.groundSearchDisabled) {
            console.log(`[WORLD-GROUND] Aucun sol trouvé à (${x.toFixed(2)}, ${z.toFixed(2)}) après vérification de 128 niveaux`);
        }
        return null;
    }

    // Méthode pour désactiver complètement la recherche de sol
    disableGroundSearch() {
        this.groundSearchDisabled = true;
        console.log(`[DEBUG] Recherche de sol désactivée définitivement dans World.js - Plus de logs "Sol trouvé à..."`);
    }

    // Méthode pour déterminer si un type de bloc est grimpable (sol uniquement, pas les arbres)
    isClimbableBlock(blockType) {
        // Types de blocs de sol grimpables
        const CLIMBABLE_BLOCKS = [
            1,  // STONE
            2,  // DIRT
            3,  // GRASS
            4,  // SAND
            8,  // SNOW
            9,  // ICE
            10, // CLAY
            11, // GRAVEL
            12, // COBBLESTONE
            13, // BEDROCK
            14, // COAL_ORE
            15, // IRON_ORE
            16, // GOLD_ORE
            17  // DIAMOND_ORE
        ];

        return CLIMBABLE_BLOCKS.includes(blockType);
    }

    // Méthode pour obtenir le type de bloc à une position donnée
    getBlockTypeAt(x, y, z) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;

        if (!this.chunks.has(key)) {
            return 0; // Air si le chunk n'existe pas
        }

        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;

        if (!chunk || !chunk.blocks) {
            return 0; // Air si pas de blocs
        }

        // Calculer les coordonnées locales dans le chunk
        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;

        // S'assurer que les coordonnées locales sont dans les limites
        if (localX < 0) localX = 0;
        if (localX >= 16) localX = 15;
        if (localZ < 0) localZ = 0;
        if (localZ >= 16) localZ = 15;
        if (y < 0) return 0;
        if (y >= 128) return 0;

        return chunk.getBlockAt(localX, y, localZ);
    }

    // Méthode pour définir un bloc à une position donnée (pour le minage)
    setBlockAt(x, y, z, blockType) {
        const chunkX = Math.floor(x / 16);
        const chunkZ = Math.floor(z / 16);
        const key = `${chunkX},${chunkZ}`;

        if (!this.chunks.has(key)) {
            console.log(`Chunk ${key} n'existe pas pour setBlockAt`);
            return false;
        }

        const chunkData = this.chunks.get(key);
        const chunk = chunkData.chunk;

        if (!chunk || !chunk.blocks) {
            console.log(`Chunk ${key} n'a pas de blocs pour setBlockAt`);
            return false;
        }

        // Calculer les coordonnées locales dans le chunk
        let localX = Math.floor(x) - chunkX * 16;
        let localZ = Math.floor(z) - chunkZ * 16;

        // S'assurer que les coordonnées locales sont dans les limites
        if (localX < 0) localX = 0;
        if (localX >= 16) localX = 15;
        if (localZ < 0) localZ = 0;
        if (localZ >= 16) localZ = 15;
        if (y < 0 || y >= 128) return false;

        // Définir le bloc en utilisant la méthode du chunk
        const success = chunk.setBlockAt(localX, y, localZ, blockType);
        if (!success) {
            console.log(`Échec de la modification du bloc à (${localX}, ${y}, ${localZ})`);
            return false;
        }

        // Régénérer le mesh du chunk pour refléter les changements
        if (typeof chunk.generateMesh === 'function') {
            // Utiliser la méthode moderne de régénération
            chunk.generateMesh();
        } else {
            // Méthode de fallback pour les anciens chunks
            console.log(`⚠️ Méthode generateMesh non disponible, reconstruction manuelle du mesh...`);

            // Supprimer l'ancien mesh de la scène
            if (chunkData.mesh && chunkData.mesh.parent) {
                chunkData.mesh.parent.remove(chunkData.mesh);

                // Nettoyer le mesh selon son type
                if (chunkData.mesh.isGroup) {
                    // Si c'est un groupe, nettoyer tous les enfants
                    while (chunkData.mesh.children.length > 0) {
                        const child = chunkData.mesh.children[0];
                        if (child.geometry) child.geometry.dispose();
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(m => m.dispose());
                            } else {
                                child.material.dispose();
                            }
                        }
                        chunkData.mesh.remove(child);
                    }
                } else {
                    // C'est un mesh simple
                    if (chunkData.mesh.geometry) chunkData.mesh.geometry.dispose();
                    if (chunkData.mesh.material) {
                        if (Array.isArray(chunkData.mesh.material)) {
                            chunkData.mesh.material.forEach(mat => mat.dispose());
                        } else {
                            chunkData.mesh.material.dispose();
                        }
                    }
                }
            }

            // Reconstruire le mesh avec les nouvelles données
            const newMesh = chunk.buildMesh();
            if (newMesh) {
                this.scene.add(newMesh);
                chunkData.mesh = newMesh;
                console.log(`🔄 Mesh du chunk (${chunkX}, ${chunkZ}) reconstruit avec succès`);
            } else {
                console.log(`⚠️ Échec de la reconstruction du mesh pour le chunk (${chunkX}, ${chunkZ})`);
            }
        }

        console.log(`🔨 Bloc modifié à (${x}, ${y}, ${z}): ${blockType === 0 ? 'détruit' : 'placé'}`);
        return true;
    }

    // Méthode pour nettoyer le cache de hauteur du sol périodiquement
    cleanGroundHeightCache() {
        const now = Date.now();
        if (!this.lastCacheCleanup || now - this.lastCacheCleanup > 30000) { // 30 secondes
            this.groundHeightCache = {};
            this.lastCacheCleanup = now;
            console.log('[WORLD] Cache de hauteur du sol nettoyé');
        }
    }

    // Méthode pour désactiver temporairement les logs de recherche de sol
    disableGroundSearchLogs() {
        this.groundSearchDisabled = true;
        setTimeout(() => {
            this.groundSearchDisabled = false;
        }, 5000); // Réactiver après 5 secondes
    }
}