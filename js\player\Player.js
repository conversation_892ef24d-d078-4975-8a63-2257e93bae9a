// Three.js est chargé globalement depuis le CDN
const THREE = window.THREE;

export class Player {
    constructor(scene) {
        // Récupérer le logger global
        this.logger = window.GameLogger;

        const PLAYER_VERSION = `Player-v${Date.now()}`;
        this.logger.info('Player initialisé', {
            version: PLAYER_VERSION,
            features: {
                escaladeAutomatique: 'Désactivée après stabilisation',
                gravite: 'Désactivée après stabilisation',
                limiteInferieure: 'Désactivée après stabilisation',
                logsDetailles: 'Activés pour diagnostic'
            }
        });

        // Stocker la version pour vérification
        this.version = PLAYER_VERSION;

        // Enregistrer ce module auprès du système de versioning (si disponible)
        if (window.VERSION_CONFIG && window.VERSION_CONFIG.verifyModuleIntegrity) {
            const moduleRegistry = window.VERSION_CONFIG.verifyModuleIntegrity();
            moduleRegistry.registerModule('Player', PLAYER_VERSION);
        }

        // Configuration améliorée de la caméra
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

        // Position de départ du joueur au niveau du sol (sera ajustée automatiquement)
        this.camera.position.set(0, 100, 0); // Position temporaire haute, sera ajustée au premier update

        // Propriétés de mouvement et collision
        this.velocity = new THREE.Vector3();
        this.onGround = false;
        this.eyeHeight = 1.7; // Hauteur des yeux par rapport au sol (en blocs)
        this.playerHeight = 1.8; // Taille du joueur (1.8 blocs)
        this.stepHeight = 0.5; // Hauteur max d'escalade automatique (0.5 bloc)
        this.collisionRadius = 0.3; // Rayon de collision horizontal
        this.collisionSegments = 8; // Nombre de points de collision pour une détection précise
        this.needsGroundCheck = true; // Flag pour vérifier le sol au premier update
        this.flyMode = false; // Mode vol activé/désactivé

        // Options d'escalade (seront mises à jour par OptionsManager)
        this.autoClimb = true; // Escalade automatique du terrain
        this.autoClimbTrees = false; // Escalade automatique des arbres

        // Cache pour éviter les appels répétés à getGroundHeightAt
        this.lastGroundCheck = { x: null, z: null, result: null, time: 0 };
        this.groundCheckCooldown = 500; // ms entre les vérifications (augmenté)

        // Système de stabilisation pour réduire les logs
        this.isStable = false;
        this.stableFrameCount = 0;
        this.requiredStableFrames = 60; // 1 seconde à 60fps

        // Protection contre l'escalade répétée
        this.lastStepTime = 0;
        this.stepCooldown = 100; // 100ms entre les escalades

        // Système de minage
        this.isMining = false;
        this.miningStartTime = 0;
        this.miningTarget = null;
        this.miningProgress = 0;
        this.miningDuration = 1000; // 1 seconde pour miner un bloc
        this.reach = 5; // Portée de minage en blocs

        // Animation de bras
        this.armSwingTime = 0;
        this.armSwingDuration = 300; // 300ms par swing
        this.isSwinging = false;

        // Inventaire
        this.inventory = new Map();
        this.inventoryOpen = false;
        this.maxInventorySlots = 36; // 9x4 comme Minecraft

        // Interface utilisateur
        this.createUI();

        // Système de stabilisation forcée pour éviter les chutes infinies
        this.lastStablePosition = { x: 0, y: 70, z: 0 };
        this.fallTime = 0;
        this.maxFallTime = 2000; // 2 secondes max de chute

        // Système de désactivation de la physique après stabilisation
        this.physicsEnabled = true;
        this.groundPhysicsDisabled = false;

        // Orientation initiale de la caméra (regarder horizontalement)
        this.camera.rotation.order = 'YXZ'; // Ordre de rotation important pour FPS
        this.camera.rotation.set(0, 0, 0);

        // Ajout de la caméra à la scène
        scene.add(this.camera);

        // Debug: vérifier l'initialisation
        this.logger.info('Caméra initialisée', {
            position: {
                x: this.camera.position.x,
                y: this.camera.position.y,
                z: this.camera.position.z
            },
            rotation: {
                x: this.camera.rotation.x,
                y: this.camera.rotation.y,
                z: this.camera.rotation.z
            }
        });

        // Le redimensionnement est maintenant géré par le système principal dans main.js
        // Cette méthode peut être appelée depuis main.js pour mettre à jour la caméra
        this.updateCameraAspect = (width, height) => {
            this.camera.aspect = width / height;
            this.camera.updateProjectionMatrix();

            this.logger.debug('Aspect ratio de la caméra mis à jour', {
                width: width,
                height: height,
                aspect: this.camera.aspect.toFixed(3),
                fov: this.camera.fov
            });
        };
    }

    update(delta, world) {
        // Vérifier que delta est valide
        if (isNaN(delta) || delta <= 0) {
            delta = 0.016; // Valeur par défaut
        }

        // Logs intelligents avec le nouveau système
        if (window.SmartLogger) {
            if (this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1) {
                window.SmartLogger.debug('PHYSICS', 'Mise à jour joueur', {
                    position: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    velocity: {
                        y: Math.round(this.velocity.y * 1000) / 1000
                    },
                    onGround: this.onGround,
                    needsGroundCheck: this.needsGroundCheck
                });
            }
        } else {
            // Fallback vers l'ancien système avec throttling
            if ((this.needsGroundCheck || Math.abs(this.velocity.y) > 0.1) && 
                (!this.lastLogTime || Date.now() - this.lastLogTime > 1000)) {
                this.logger.physics('Mise à jour joueur', {
                    position: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    velocity: {
                        y: Math.round(this.velocity.y * 1000) / 1000
                    },
                    onGround: this.onGround,
                    needsGroundCheck: this.needsGroundCheck
                });
                this.lastLogTime = Date.now();
            }
        }

        // Au premier update, positionner le joueur sur le sol
        if (this.needsGroundCheck && world && !this.flyMode) {
            this.logger.physics('Début du positionnement initial');
            const positioningSuccess = this.findGroundPosition(world);

            if (positioningSuccess) {
                // Marquer le positionnement initial comme terminé
                this.needsGroundCheck = false;
                this.onGround = true;
                this.velocity.y = 0;

                this.logger.physics('Positionnement initial réussi', {
                    finalPosition: {
                        x: Math.round(this.camera.position.x * 100) / 100,
                        y: Math.round(this.camera.position.y * 100) / 100,
                        z: Math.round(this.camera.position.z * 100) / 100
                    },
                    physicsEnabled: true
                });
            } else {
                this.logger.warn('Positionnement initial échoué', {
                    reason: 'Chunk pas encore généré',
                    action: 'Attente de génération des chunks'
                });
            }
        }

        // Système de physique simplifié et robuste
        if (!this.flyMode && world) {
            const playerPos = this.camera.position;
            const feetY = playerPos.y - this.eyeHeight;

            // Essayer d'abord getGroundHeightAt
            let groundHeight = world.getGroundHeightAt(playerPos.x, playerPos.z);

            // Si getGroundHeightAt échoue, chercher manuellement dans le chunk
            if (groundHeight === null || groundHeight === undefined) {
                const chunkX = Math.floor(playerPos.x / 16);
                const chunkZ = Math.floor(playerPos.z / 16);
                const chunkKey = `${chunkX},${chunkZ}`;

                if (world.chunks.has(chunkKey)) {
                    const chunk = world.chunks.get(chunkKey).chunk;
                    if (chunk && chunk.getBlockAt) {
                        const localX = Math.max(0, Math.min(15, Math.floor(playerPos.x) - chunkX * 16));
                        const localZ = Math.max(0, Math.min(15, Math.floor(playerPos.z) - chunkZ * 16));

                        // Chercher le sol de haut en bas
                        for (let y = Math.min(127, Math.floor(feetY) + 5); y >= 0; y--) {
                            try {
                                const blockType = chunk.getBlockAt(localX, y, localZ);
                                if (blockType && blockType !== 0) {
                                    groundHeight = y;
                                    break;
                                }
                            } catch (e) {
                                continue;
                            }
                        }
                    }
                }
            }

            // Appliquer la physique basée sur le sol trouvé
            if (groundHeight !== null && groundHeight !== undefined) {
                const distanceToGround = feetY - (groundHeight + 1);

                if (distanceToGround <= 0.1 && this.velocity.y <= 0) {
                    // Sur le sol seulement si on descend ou est immobile
                    this.camera.position.y = groundHeight + 1 + this.eyeHeight;
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;

                    // Sauvegarder position stable
                    this.lastStablePosition = {
                        x: playerPos.x,
                        y: this.camera.position.y,
                        z: playerPos.z
                    };
                } else if (distanceToGround > 0.1 || this.velocity.y > 0) {
                    // En l'air ou en train de sauter - appliquer gravité seulement si on descend
                    this.onGround = false;

                    // Appliquer la gravité seulement si on n'est pas en train de monter
                    if (this.velocity.y <= 0) {
                        this.velocity.y -= 30 * delta;
                        if (this.velocity.y < -30) this.velocity.y = -30;
                    }

                    // Protection contre chute infinie
                    this.fallTime += delta * 1000;
                    if (this.fallTime > 2000) {
                        // Repositionnement d'urgence après 2 secondes
                        this.camera.position.y = groundHeight + 1 + this.eyeHeight;
                        this.velocity.y = 0;
                        this.onGround = true;
                        this.fallTime = 0;
                    }
                }
            } else {
                // Pas de sol détecté - gravité avec protection renforcée
                this.onGround = false;
                this.velocity.y -= 30 * delta;
                if (this.velocity.y < -30) this.velocity.y = -30;

                // Protection contre chute infinie
                this.fallTime += delta * 1000;
                if (this.fallTime > 3000) {
                    // Repositionnement d'urgence vers position stable ou origine
                    if (this.lastStablePosition && this.lastStablePosition.y > 0) {
                        this.camera.position.copy(this.lastStablePosition);
                    } else {
                        this.camera.position.set(0, 70, 0);
                    }
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;
                    this.logger.warn('Repositionnement d\'urgence effectué');
                }
            }
        } else if (!this.flyMode) {
            // Mode non-vol sans monde - gravité simple
            this.velocity.y -= 30 * delta;
            if (this.velocity.y < -30) this.velocity.y = -30;
        }

        // Position avant mouvement (pour détection de collision)
        const previousPosition = this.camera.position.clone();

        // Décomposition du mouvement en étapes séparées (horizontal et vertical)
        const movement = this.velocity.clone().multiplyScalar(delta);

        // 1. Appliquer le mouvement horizontal (X et Z) d'abord
        const horizontalMovement = new THREE.Vector3(movement.x, 0, movement.z);
        this.camera.position.add(horizontalMovement);

        // Collision horizontale simplifiée - permettre mouvement normal
        if (!this.flyMode && world && world.hasCollisionAt) {
            const playerPos = this.camera.position;
            const radius = this.collisionRadius;
            const feetY = playerPos.y - this.eyeHeight;

            // Vérifier collision à hauteur de poitrine (plus haut pour éviter détection sur terrain plat)
            const chestY = feetY + 1.3; // Hauteur de la poitrine du joueur
            const hasChestCollision = world.hasCollisionAt(playerPos.x, chestY, playerPos.z, radius);

            if (hasChestCollision) {
                // Obstacle au niveau de la poitrine - vérifier si on peut escalader
                let canClimb = false;

                if (this.onGround && Math.abs(this.velocity.y) < 0.1) {
                    // Obtenir le type de bloc qui bloque
                    const blockY = Math.floor(chestY);
                    const blockType = world.getBlockTypeAt(playerPos.x, blockY, playerPos.z);

                    // Vérifier si l'escalade est autorisée selon les paramètres
                    let shouldClimb = false;

                    if (world.textureGenerator && world.textureGenerator.isTreeBlock(blockType)) {
                        // C'est un bloc d'arbre - vérifier si l'escalade d'arbres est activée
                        shouldClimb = this.autoClimbTrees;
                        this.logger.debug('Bloc d\'arbre détecté', {
                            blockType: blockType,
                            autoClimbTrees: this.autoClimbTrees,
                            willClimb: shouldClimb
                        });
                    } else if (world.isClimbableBlock && world.isClimbableBlock(blockType)) {
                        // C'est un bloc de terrain - vérifier si l'escalade de terrain est activée
                        shouldClimb = this.autoClimb;
                        this.logger.debug('Bloc de terrain détecté', {
                            blockType: blockType,
                            autoClimb: this.autoClimb,
                            willClimb: shouldClimb
                        });
                    }

                    if (shouldClimb) {
                        // Vérifier si on peut monter d'1 bloc (pas un mur de 2+ blocs)
                        const aboveChestY = feetY + 2.3;
                        const hasAboveCollision = world.hasCollisionAt(playerPos.x, aboveChestY, playerPos.z, radius);
                        if (!hasAboveCollision) {
                            canClimb = true;
                        }
                    }
                }

                if (canClimb) {
                    // Escalade automatique selon les paramètres
                    const now = performance.now();
                    if (now - this.lastStepTime > this.stepCooldown) {
                        this.camera.position.y += 1.0;
                        this.velocity.y = 0;
                        this.lastStepTime = now;

                        const blockType = world.getBlockTypeAt(playerPos.x, Math.floor(chestY), playerPos.z);
                        const isTree = world.textureGenerator && world.textureGenerator.isTreeBlock(blockType);

                        this.logger.physics('Escalade effectuée', {
                            blockType: blockType,
                            isTree: isTree,
                            position: { x: playerPos.x, y: this.camera.position.y, z: playerPos.z }
                        });
                    }
                } else {
                    // Obstacle non-grimpable - bloquer le mouvement
                    this.camera.position.x = previousPosition.x;
                    this.camera.position.z = previousPosition.z;
                    this.velocity.x = 0;
                    this.velocity.z = 0;

                    const blockType = world.getBlockTypeAt(playerPos.x, Math.floor(chestY), playerPos.z);
                    const isTree = world.textureGenerator && world.textureGenerator.isTreeBlock(blockType);

                    this.logger.debug('Mouvement bloqué par obstacle', {
                        blockType: blockType,
                        isTree: isTree,
                        reason: isTree ? 'Escalade d\'arbres désactivée' : 'Bloc non-grimpable'
                    });
                }
            }
            // Pas de collision au niveau de la poitrine = mouvement libre
        }

        // 2. Appliquer le mouvement vertical (Y) - géré par le système de physique simplifié ci-dessus
        this.camera.position.y += movement.y;

        // Collision avec le plafond seulement
        if (!this.flyMode && world && world.hasCollisionAt) {
            const playerPos = this.camera.position;
            const radius = this.collisionRadius;

            // Collision avec le plafond
            if (this.velocity.y > 0 && world.hasCollisionAt(playerPos.x, playerPos.y + 0.5, playerPos.z, radius)) {
                this.camera.position.y = previousPosition.y;
                this.velocity.y = 0;
            }
        }

        // Collision avec le sol du monde (limite inférieure) seulement si pas en mode vol
        if (!this.flyMode) {
            const minGroundLevel = this.eyeHeight;
            if (this.camera.position.y < minGroundLevel) {
                this.camera.position.y = minGroundLevel;
                this.velocity.y = 0;
                this.onGround = true;
            }
        }

        // Friction au sol
        if (this.onGround) {
            this.velocity.x *= 0.9;
            this.velocity.z *= 0.9;
        }

        // Limiter la vitesse horizontale maximale
        const maxSpeed = 20;
        const currentSpeed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.z * this.velocity.z);
        if (currentSpeed > maxSpeed) {
            const scale = maxSpeed / currentSpeed;
            this.velocity.x *= scale;
            this.velocity.z *= scale;
        }

        // Debug: vérifier que la position est valide
        if (isNaN(this.camera.position.x) || isNaN(this.camera.position.y) || isNaN(this.camera.position.z)) {
            this.logger.error('Position caméra invalide détectée', {
                invalidPosition: {
                    x: this.camera.position.x,
                    y: this.camera.position.y,
                    z: this.camera.position.z
                },
                action: 'Réinitialisation à la position de spawn'
            });
            this.camera.position.set(0, this.eyeHeight, 0);
        }

        // Mettre à jour le système de minage
        this.updateMining(delta);
    }

    // Méthode pour trouver la position du sol au démarrage (spawn dans les plaines)
    findGroundPosition(world) {
        // Utiliser une position de spawn plus proche de l'origine pour éviter les chunks non générés
        if (!this.spawnPosition) {
            // Chercher d'abord dans les chunks déjà générés
            const availableChunks = Array.from(world.chunks.keys());
            if (availableChunks.length > 0) {
                // Utiliser le premier chunk disponible proche de l'origine
                const centerChunks = availableChunks.filter(key => {
                    const [x, z] = key.split(',').map(Number);
                    return Math.abs(x) <= 2 && Math.abs(z) <= 2; // Chunks proches de l'origine
                });

                if (centerChunks.length > 0) {
                    const [chunkX, chunkZ] = centerChunks[0].split(',').map(Number);
                    this.spawnPosition = {
                        x: chunkX * 16 + 8, // Centre du chunk
                        z: chunkZ * 16 + 8
                    };
                } else {
                    // Utiliser le premier chunk disponible
                    const [chunkX, chunkZ] = availableChunks[0].split(',').map(Number);
                    this.spawnPosition = {
                        x: chunkX * 16 + 8,
                        z: chunkZ * 16 + 8
                    };
                }
            } else {
                // Fallback à l'origine
                this.spawnPosition = { x: 0, z: 0 };
            }
        }

        const startX = this.spawnPosition.x;
        const startZ = this.spawnPosition.z;

        this.logger.debug('Recherche position de spawn', {
            targetPosition: { x: startX, z: startZ },
            currentY: this.camera.position.y
        });

        // Vérifier si le chunk central existe
        const chunkX = Math.floor(startX / 16);
        const chunkZ = Math.floor(startZ / 16);
        const chunkKey = `${chunkX},${chunkZ}`;

        if (!world.chunks.has(chunkKey)) {
            this.logger.debug('Chunk de spawn pas encore généré', {
                chunkKey: chunkKey,
                availableChunks: Array.from(world.chunks.keys())
            });
            return false; // ÉCHEC - Attendre que le chunk soit généré
        }

        // Utiliser la nouvelle méthode spécialisée pour trouver le sol
        const groundHeight = world.getGroundHeightAt(startX, startZ);

        if (groundHeight !== null && groundHeight > 0) {
            const newY = groundHeight + 1 + this.eyeHeight;

            // Positionner le joueur sur le sol
            this.camera.position.x = startX;
            this.camera.position.y = newY;
            this.camera.position.z = startZ;
            this.velocity.y = 0;
            this.onGround = true;
            this.fallTime = 0; // Reset du timer de chute

            // Sauvegarder comme position stable
            this.lastStablePosition = {
                x: startX,
                y: newY,
                z: startZ
            };

            this.logger.info('Spawn réussi', {
                spawnPosition: { x: startX, y: newY, z: startZ },
                groundHeight: groundHeight,
                chunkKey: chunkKey
            });

            return true; // SUCCÈS - Positionnement réussi
        }

        // Si aucun sol trouvé, chercher manuellement dans le chunk
        const chunk = world.chunks.get(chunkKey);
        if (chunk && chunk.chunk) {
            const localX = Math.floor(startX) - chunkX * 16;
            const localZ = Math.floor(startZ) - chunkZ * 16;

            // Chercher le sol de haut en bas
            for (let y = 127; y >= 1; y--) {
                const blockType = chunk.chunk.getBlockAt(localX, y, localZ);
                if (blockType !== 0) { // Bloc solide trouvé
                    const newY = y + 1 + this.eyeHeight;
                    this.camera.position.x = startX;
                    this.camera.position.y = newY;
                    this.camera.position.z = startZ;
                    this.velocity.y = 0;
                    this.onGround = true;
                    this.fallTime = 0;

                    this.lastStablePosition = {
                        x: startX,
                        y: newY,
                        z: startZ
                    };

                    this.logger.info('Sol trouvé manuellement', {
                        position: { x: startX, y: newY, z: startZ },
                        groundY: y,
                        blockType: blockType
                    });

                    return true;
                }
            }
        }

        // Si vraiment aucun sol trouvé, utiliser une hauteur par défaut
        this.camera.position.x = startX;
        this.camera.position.y = 70;
        this.camera.position.z = startZ;
        this.velocity.y = 0;
        this.onGround = true; // Forcer au sol pour éviter la chute
        this.fallTime = 0;

        this.logger.warn('Sol non trouvé, position par défaut utilisée', {
            defaultPosition: { x: startX, y: 70, z: startZ }
        });

        return true; // SUCCÈS - Position par défaut utilisée
    }

    // Trouver une position de spawn dans un biome de plaines
    findPlainsSpawnPosition(world) {
        if (!world.generator) {
            return { x: 0, z: 0 }; // Position par défaut si pas de générateur
        }

        // Chercher dans un rayon autour de l'origine
        const searchRadius = 100;
        const maxAttempts = 50;

        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const angle = (attempt / maxAttempts) * Math.PI * 2;
            const distance = (attempt / maxAttempts) * searchRadius;

            const x = Math.floor(Math.cos(angle) * distance);
            const z = Math.floor(Math.sin(angle) * distance);

            const biomeValue = world.generator.getBiomeValue(x, z);
            const biome = world.generator.getBiome(biomeValue);

            // Chercher spécifiquement les plaines
            if (biome.name === 'Plains') {
                this.logger.info('Position de spawn trouvée dans les plaines', {
                    position: { x, z },
                    biome: biome.name,
                    attempt: attempt + 1
                });
                return { x, z };
            }
        }

        // Si aucune plaine trouvée, utiliser la position par défaut
        this.logger.warn('Aucune plaine trouvée, utilisation position par défaut');
        return { x: 0, z: 0 };
    }

    // Méthode pour trouver le sol en dessous du joueur avec cache amélioré
    findGroundBelow(world, x, currentY, z) {
        const now = performance.now();

        // Utiliser une grille plus large pour le cache (blocs de 4x4)
        const cacheX = Math.floor(x / 4) * 4;
        const cacheZ = Math.floor(z / 4) * 4;

        // Vérifier le cache si on est dans la même zone et que le cooldown n'est pas écoulé
        if (this.lastGroundCheck.x === cacheX &&
            this.lastGroundCheck.z === cacheZ &&
            now - this.lastGroundCheck.time < this.groundCheckCooldown) {

            // Utiliser le résultat en cache
            const cachedResult = this.lastGroundCheck.result;
            if (cachedResult !== null && cachedResult <= currentY + 0.5) {
                return cachedResult;
            }
            return null;
        }

        // Faire un nouvel appel à getGroundHeightAt
        const groundHeight = world.getGroundHeightAt(x, z);

        // Mettre à jour le cache seulement si on a un résultat valide
        if (groundHeight !== null) {
            this.lastGroundCheck = {
                x: cacheX,
                z: cacheZ,
                result: groundHeight,
                time: now
            };
        }

        if (groundHeight !== null && groundHeight <= currentY + 0.5) {
            return groundHeight; // Retourner la hauteur du bloc solide
        }

        return null; // Aucun sol trouvé
    }

    // Méthode pour vérifier si un bloc est visible par le joueur
    canSeeBlock(blockX, blockY, blockZ, maxDistance = 100) {
        const position = this.camera.position;
        const distance = Math.sqrt(
            Math.pow(blockX - position.x, 2) +
            Math.pow(blockY - position.y, 2) +
            Math.pow(blockZ - position.z, 2)
        );

        // Vérifier la distance maximale
        return distance <= maxDistance;
    }

    // ===== MÉTHODES DE SECOURS =====

    // Méthode pour téléporter le joueur sur la surface (fonction de secours)
    emergencySpawnOnSurface(world) {
        console.log(`🚁 [PLAYER-EMERGENCY] Spawn d'urgence activé`);

        const currentX = this.camera.position.x;
        const currentZ = this.camera.position.z;

        // Essayer de trouver le sol à la position actuelle
        const groundHeight = world.getGroundHeightAt(currentX, currentZ);

        if (groundHeight !== null) {
            const newY = groundHeight + 1 + this.eyeHeight;
            this.camera.position.y = newY;
            this.velocity.y = 0;
            this.onGround = true;
            // Physique normale

            console.log(`🚁 [PLAYER-EMERGENCY] ✅ Spawn réussi à (${currentX.toFixed(2)}, ${newY.toFixed(2)}, ${currentZ.toFixed(2)})`);
            return true;
        } else {
            // Position de secours au spawn par défaut
            this.camera.position.set(0, 70, 0);
            this.velocity.y = 0;
            this.onGround = true;


            console.log(`🚁 [PLAYER-EMERGENCY] ⚠️ Spawn de secours à (0, 70, 0)`);
            return false;
        }
    }

    // Méthode pour réinitialiser complètement la physique du joueur
    resetAllPhysics() {
        console.log(`⚙️ [PLAYER-RESET] Réinitialisation complète de la physique`);

        this.needsGroundCheck = false;
        this.isStable = false;
        this.stableFrameCount = 0;
        this.fallTime = 0;
        this.velocity.set(0, 0, 0);
        this.onGround = false;

        console.log(`⚙️ [PLAYER-RESET] ✅ Physique réinitialisée`);
    }

    // ===== SYSTÈME DE MINAGE =====

    // Créer l'interface utilisateur
    createUI() {
        // Le crosshair est déjà défini dans le HTML, pas besoin de le recréer

        // Créer l'interface d'inventaire
        this.createInventoryUI();

        // Créer la barre de progression de minage
        this.createMiningProgressBar();

        // Créer la hotbar (barre d'outils)
        this.createHotbar();
    }

    // Créer la barre de progression de minage
    createMiningProgressBar() {
        const progressContainer = document.createElement('div');
        progressContainer.id = 'mining-progress';
        progressContainer.style.cssText = `
            position: fixed;
            top: 60%;
            left: 50%;
            width: 200px;
            height: 20px;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid white;
            border-radius: 10px;
            display: none;
            z-index: 1000;
        `;

        const progressBar = document.createElement('div');
        progressBar.id = 'mining-progress-bar';
        progressBar.style.cssText = `
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 8px;
            transition: width 0.1s ease;
        `;

        progressContainer.appendChild(progressBar);
        document.body.appendChild(progressContainer);
    }

    // Créer la hotbar
    createHotbar() {
        const hotbar = document.createElement('div');
        hotbar.id = 'hotbar';
        hotbar.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 2px;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px;
            border-radius: 5px;
            z-index: 1000;
        `;

        // Créer 9 slots pour la hotbar
        for (let i = 0; i < 9; i++) {
            const slot = document.createElement('div');
            slot.className = 'hotbar-slot';
            slot.dataset.slot = i;
            slot.style.cssText = `
                width: 40px;
                height: 40px;
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 12px;
                cursor: pointer;
            `;

            if (i === 0) {
                slot.style.border = '2px solid white'; // Slot sélectionné
            }

            hotbar.appendChild(slot);
        }

        document.body.appendChild(hotbar);
        this.selectedSlot = 0;
    }

    // Créer l'interface d'inventaire
    createInventoryUI() {
        const inventoryContainer = document.createElement('div');
        inventoryContainer.id = 'inventory';
        inventoryContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 300px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid white;
            border-radius: 10px;
            padding: 20px;
            display: none;
            z-index: 2000;
        `;

        const title = document.createElement('h3');
        title.textContent = 'Inventaire';
        title.style.cssText = `
            color: white;
            margin: 0 0 15px 0;
            text-align: center;
        `;

        const grid = document.createElement('div');
        grid.id = 'inventory-grid';
        grid.style.cssText = `
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            gap: 2px;
            height: 200px;
        `;

        // Créer 36 slots d'inventaire (4 rangées de 9)
        for (let i = 0; i < 36; i++) {
            const slot = document.createElement('div');
            slot.className = 'inventory-slot';
            slot.dataset.slot = i;
            slot.style.cssText = `
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 10px;
                cursor: pointer;
                min-height: 35px;
            `;
            grid.appendChild(slot);
        }

        inventoryContainer.appendChild(title);
        inventoryContainer.appendChild(grid);
        document.body.appendChild(inventoryContainer);
    }

    // Commencer le minage
    startMining() {
        if (this.isMining) return;

        const target = this.getTargetBlock();
        if (!target) return;

        this.isMining = true;
        this.miningTarget = target;
        this.miningStartTime = performance.now();
        this.miningProgress = 0;

        // Afficher la barre de progression
        const progressContainer = document.getElementById('mining-progress');
        if (progressContainer) {
            progressContainer.style.display = 'block';
        }

        // Commencer l'animation de bras
        this.startArmSwing();

        console.log(`🔨 Début du minage du bloc ${target.blockType} à (${target.x}, ${target.y}, ${target.z})`);
    }

    // Arrêter le minage
    stopMining() {
        if (!this.isMining) return;

        this.isMining = false;
        this.miningTarget = null;
        this.miningProgress = 0;

        // Cacher la barre de progression
        const progressContainer = document.getElementById('mining-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }

        // Arrêter l'animation de bras
        this.stopArmSwing();

        console.log(`🔨 Arrêt du minage`);
    }

    // Obtenir le bloc visé
    getTargetBlock() {
        const raycaster = new THREE.Raycaster();
        const direction = new THREE.Vector3();

        // Obtenir la direction de la caméra
        this.camera.getWorldDirection(direction);
        raycaster.set(this.camera.position, direction);

        // Chercher le bloc le plus proche dans la portée
        for (let distance = 1; distance <= this.reach; distance += 0.5) {
            const testPos = this.camera.position.clone().add(direction.clone().multiplyScalar(distance));
            const blockX = Math.floor(testPos.x);
            const blockY = Math.floor(testPos.y);
            const blockZ = Math.floor(testPos.z);

            // Vérifier si il y a un bloc à cette position
            const blockType = window.world?.getBlockTypeAt(blockX, blockY, blockZ);
            if (blockType && blockType !== 0) { // 0 = air
                return {
                    x: blockX,
                    y: blockY,
                    z: blockZ,
                    blockType: blockType,
                    distance: distance
                };
            }
        }

        return null;
    }

    // Commencer l'animation de bras
    startArmSwing() {
        this.isSwinging = true;
        this.armSwingTime = 0;
    }

    // Arrêter l'animation de bras
    stopArmSwing() {
        this.isSwinging = false;
        this.armSwingTime = 0;
    }

    // Mettre à jour le système de minage
    updateMining(delta) {
        if (!this.isMining || !this.miningTarget) return;

        const now = performance.now();
        const elapsed = now - this.miningStartTime;
        this.miningProgress = Math.min(elapsed / this.miningDuration, 1.0);

        // Mettre à jour la barre de progression
        const progressBar = document.getElementById('mining-progress-bar');
        if (progressBar) {
            progressBar.style.width = `${this.miningProgress * 100}%`;
        }

        // Vérifier si le minage est terminé
        if (this.miningProgress >= 1.0) {
            this.completeMining();
        }

        // Mettre à jour l'animation de bras
        if (this.isSwinging) {
            this.armSwingTime += delta * 1000;
            if (this.armSwingTime >= this.armSwingDuration) {
                this.armSwingTime = 0;
            }
        }
    }

    // Terminer le minage
    completeMining() {
        if (!this.miningTarget) return;

        const { x, y, z, blockType } = this.miningTarget;

        // Détruire le bloc dans le monde
        if (window.world && window.world.setBlockAt) {
            window.world.setBlockAt(x, y, z, 0); // 0 = air
        }

        // Ajouter le bloc à l'inventaire
        this.addToInventory(blockType, 1);

        console.log(`✅ Bloc miné avec succès: type ${blockType} à (${x}, ${y}, ${z})`);

        // Arrêter le minage
        this.stopMining();
    }

    // ===== SYSTÈME D'INVENTAIRE =====

    // Ajouter un objet à l'inventaire
    addToInventory(blockType, quantity = 1) {
        const blockName = this.getBlockName(blockType);

        if (this.inventory.has(blockType)) {
            this.inventory.set(blockType, this.inventory.get(blockType) + quantity);
        } else {
            this.inventory.set(blockType, quantity);
        }

        console.log(`📦 Ajouté à l'inventaire: ${quantity}x ${blockName}`);
        this.updateInventoryUI();
        this.updateHotbarUI();
    }

    // Obtenir le nom d'un bloc
    getBlockName(blockType) {
        const blockNames = {
            1: 'Pierre',
            2: 'Terre',
            3: 'Herbe',
            4: 'Sable',
            6: 'Bois',
            7: 'Feuilles',
            8: 'Neige',
            9: 'Glace',
            10: 'Argile',
            11: 'Gravier',
            12: 'Pierre Taillée',
            13: 'Bedrock',
            14: 'Minerai de Charbon',
            15: 'Minerai de Fer',
            16: 'Minerai d\'Or',
            17: 'Minerai de Diamant',
            18: 'Bois de Chêne',
            19: 'Bois de Bouleau',
            20: 'Bois de Pin',
            21: 'Feuilles de Chêne',
            22: 'Feuilles de Bouleau',
            23: 'Feuilles de Pin',
            24: 'Cactus',
            25: 'Herbe Haute',
            26: 'Fleurs',
            27: 'Champignon',
            28: 'Lave'
        };

        return blockNames[blockType] || `Bloc ${blockType}`;
    }

    // Mettre à jour l'interface d'inventaire
    updateInventoryUI() {
        const slots = document.querySelectorAll('.inventory-slot');
        let slotIndex = 0;

        // Vider tous les slots
        slots.forEach(slot => {
            slot.textContent = '';
            slot.style.background = 'rgba(255, 255, 255, 0.1)';
        });

        // Remplir avec les objets de l'inventaire
        for (const [blockType, quantity] of this.inventory) {
            if (slotIndex >= slots.length) break;

            const slot = slots[slotIndex];
            const blockName = this.getBlockName(blockType);

            slot.textContent = `${blockName}\n${quantity}`;
            slot.style.background = 'rgba(100, 150, 200, 0.3)';
            slot.dataset.blockType = blockType;
            slot.dataset.quantity = quantity;

            slotIndex++;
        }
    }

    // Mettre à jour la hotbar
    updateHotbarUI() {
        const slots = document.querySelectorAll('.hotbar-slot');
        let slotIndex = 0;

        // Vider tous les slots
        slots.forEach(slot => {
            slot.textContent = '';
        });

        // Remplir avec les premiers objets de l'inventaire
        for (const [blockType, quantity] of this.inventory) {
            if (slotIndex >= 9) break;

            const slot = slots[slotIndex];
            const blockName = this.getBlockName(blockType);

            slot.textContent = `${blockName.substring(0, 3)}\n${quantity}`;
            slot.dataset.blockType = blockType;
            slot.dataset.quantity = quantity;

            slotIndex++;
        }
    }

    // Toggle inventaire
    toggleInventory() {
        this.inventoryOpen = !this.inventoryOpen;
        const inventory = document.getElementById('inventory');

        if (inventory) {
            inventory.style.display = this.inventoryOpen ? 'block' : 'none';
        }

        console.log(`📦 Inventaire ${this.inventoryOpen ? 'ouvert' : 'fermé'}`);
    }
}