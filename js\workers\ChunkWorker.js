// ChunkWorker.js - Web Worker pour la génération de chunks
// Bruit de Perlin simplifié pour le worker (pas d'imports dans les workers)

const BLOCK_TYPES = {
    AIR: 0,
    STONE: 1,
    DIRT: 2,
    GRASS: 3,
    SAND: 4,
    WATER: 5,
    WOOD: 6,
    LEAVES: 7,
    SNOW: 8,
    ICE: 9,
    CLAY: 10,
    GRAVEL: 11,
    COBBLESTONE: 12,
    BEDROCK: 13,
    COAL_ORE: 14,
    IRON_ORE: 15,
    GOLD_ORE: 16,
    DIAMOND_ORE: 17,
    OAK_WOOD: 18,
    BIRCH_WOOD: 19,
    PINE_WOOD: 20,
    OAK_LEAVES: 21,
    BIRCH_LEAVES: 22,
    PINE_LEAVES: 23,
    CACTUS: 24,
    TALL_GRASS: 25,
    FLOWERS: 26,
    MUSHROOM: 27
};

class Noise {
    constructor(seed) {
        this.seed = seed || Math.floor(Math.random() * 65536);
        this.gradients = {};
    }
    
    hash(x, y) {
        return ((Math.sin(x * 12.9898 + y * 78.233 + this.seed) * 43758.5453) % 1);
    }
    
    getGradient(x, y) {
        const key = `${x},${y}`;
        if (!this.gradients[key]) {
            const theta = this.hash(x, y) * Math.PI * 2;
            this.gradients[key] = { x: Math.cos(theta), y: Math.sin(theta) };
        }
        return this.gradients[key];
    }
    
    dotGridGradient(ix, iy, x, y) {
        const gradient = this.getGradient(ix, iy);
        const dx = x - ix;
        const dy = y - iy;
        return dx * gradient.x + dy * gradient.y;
    }
    
    smootherstep(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }
    
    lerp(a, b, t) {
        return a + t * (b - a);
    }
    
    perlin(x, y) {
        const x0 = Math.floor(x);
        const y0 = Math.floor(y);
        const x1 = x0 + 1;
        const y1 = y0 + 1;
        
        const sx = this.smootherstep(x - x0);
        const sy = this.smootherstep(y - y0);
        
        const n0 = this.dotGridGradient(x0, y0, x, y);
        const n1 = this.dotGridGradient(x1, y0, x, y);
        const ix0 = this.lerp(n0, n1, sx);
        
        const n2 = this.dotGridGradient(x0, y1, x, y);
        const n3 = this.dotGridGradient(x1, y1, x, y);
        const ix1 = this.lerp(n2, n3, sx);
        
        const value = this.lerp(ix0, ix1, sy);
        return value;
    }
}

class WorldGenerator {
    constructor() {
        // Générateurs de bruit pour différentes couches de terrain
        this.baseNoise = new Noise(42);
        this.detailNoise = new Noise(43);
        this.biomeNoise = new Noise(44);
        this.mountainNoise = new Noise(45);
        this.caveNoise = new Noise(46);
        this.oreNoise = new Noise(47);
        
        // Paramètres de terrain améliorés
        this.baseAmplitude = 25;
        this.detailAmplitude = 4;
        this.mountainAmplitude = 40;
        this.waterLevel = 60;
        
        // Échelles de bruit
        this.baseScale = 0.004;
        this.detailScale = 0.025;
        this.biomeScale = 0.0015;
        this.mountainScale = 0.002;
        this.caveScale = 0.05;
        this.oreScale = 0.1;
        
        // Définition des biomes
        this.biomes = {
            OCEAN: { min: 0.0, max: 0.15, name: 'Ocean' },
            BEACH: { min: 0.15, max: 0.25, name: 'Beach' },
            PLAINS: { min: 0.25, max: 0.45, name: 'Plains' },
            FOREST: { min: 0.45, max: 0.65, name: 'Forest' },
            HILLS: { min: 0.65, max: 0.8, name: 'Hills' },
            MOUNTAINS: { min: 0.8, max: 1.0, name: 'Mountains' }
        };
    }

    getHeight(x, z) {
        const biomeValue = this.getBiomeValue(x, z);
        const biome = this.getBiome(biomeValue);
        
        const baseNoise = this.baseNoise.perlin(x * this.baseScale, z * this.baseScale);
        
        let baseHeight = this.waterLevel;
        let amplitude = this.baseAmplitude;
        
        switch (biome.name) {
            case 'Ocean':
                baseHeight = this.waterLevel - 15;
                amplitude = 8;
                break;
            case 'Beach':
                baseHeight = this.waterLevel - 2;
                amplitude = 3;
                break;
            case 'Plains':
                baseHeight = this.waterLevel + 5;
                amplitude = 12;
                break;
            case 'Forest':
                baseHeight = this.waterLevel + 8;
                amplitude = 18;
                break;
            case 'Hills':
                baseHeight = this.waterLevel + 15;
                amplitude = 25;
                break;
            case 'Mountains':
                baseHeight = this.waterLevel + 25;
                amplitude = this.mountainAmplitude;
                const mountainNoise = this.mountainNoise.perlin(x * this.mountainScale, z * this.mountainScale);
                baseHeight += mountainNoise * 20;
                break;
        }
        
        const finalHeight = baseHeight + (baseNoise * amplitude);
        return Math.floor(Math.max(10, finalHeight));
    }
    
    getDetailNoise(x, z) {
        return this.detailNoise.perlin(x * this.detailScale, z * this.detailScale) * this.detailAmplitude;
    }
    
    getBiomeValue(x, z) {
        return (this.biomeNoise.perlin(x * this.biomeScale, z * this.biomeScale) + 1) * 0.5;
    }
    
    getBiome(biomeValue) {
        for (const biome of Object.values(this.biomes)) {
            if (biomeValue >= biome.min && biomeValue < biome.max) {
                return biome;
            }
        }
        return this.biomes.PLAINS;
    }
    
    shouldHaveCave(x, y, z) {
        if (y > 45 || y < 8) return false; // Réduire la zone de grottes pour éviter les trous en surface
        const caveNoise = this.caveNoise.perlin(x * this.caveScale, y * this.caveScale * 2, z * this.caveScale);
        return caveNoise > 0.65; // Seuil plus élevé pour moins de grottes
    }
    
    getOreType(x, y, z) {
        if (y > 40) return null;
        const oreNoise = this.oreNoise.perlin(x * this.oreScale, y * this.oreScale, z * this.oreScale);
        
        if (y < 12) {
            if (oreNoise > 0.85) return 'DIAMOND_ORE';
            if (oreNoise > 0.75) return 'GOLD_ORE';
        }
        
        if (y < 25) {
            if (oreNoise > 0.8) return 'IRON_ORE';
            if (oreNoise > 0.7) return 'GOLD_ORE';
        }
        
        if (y < 40) {
            if (oreNoise > 0.75) return 'COAL_ORE';
            if (oreNoise > 0.85) return 'IRON_ORE';
        }
        
        return null;
    }
    
    getTreeType(biomeValue) {
        const biome = this.getBiome(biomeValue);
        
        switch (biome.name) {
            case 'Forest':
                return Math.random() < 0.7 ? 'OAK' : 'BIRCH';
            case 'Hills':
                return Math.random() < 0.8 ? 'PINE' : 'OAK';
            case 'Mountains':
                return 'PINE';
            default:
                return 'OAK';
        }
    }
}

function getIndex(x, y, z) {
    return x * 128 * 16 + y * 16 + z; 
}

// Générer la végétation selon le biome
function generateVegetation(blocks, x, z, height, biome, biomeValue, worldGen) {
    switch (biome.name) {
        case 'Plains':
            if (Math.random() < 0.3) {
                blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.TALL_GRASS;
            }
            if (Math.random() < 0.05) {
                blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.FLOWERS;
            }
            if (Math.random() < 0.01) {
                const treeType = worldGen.getTreeType(biomeValue);
                generateTree(blocks, x, height + 1, z, treeType);
            }
            break;
            
        case 'Forest':
            if (Math.random() < 0.15) {
                const treeType = worldGen.getTreeType(biomeValue);
                generateTree(blocks, x, height + 1, z, treeType);
            }
            if (Math.random() < 0.02) {
                blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.MUSHROOM;
            }
            break;
            
        case 'Hills':
            if (Math.random() < 0.08) {
                const treeType = worldGen.getTreeType(biomeValue);
                generateTree(blocks, x, height + 1, z, treeType);
            }
            if (Math.random() < 0.2) {
                blocks[getIndex(x, height + 1, z)] = BLOCK_TYPES.TALL_GRASS;
            }
            break;
            
        case 'Mountains':
            if (height < 85 && Math.random() < 0.03) {
                generateTree(blocks, x, height + 1, z, 'PINE');
            }
            break;
            
        case 'Beach':
            if (Math.random() < 0.01) {
                generateCactus(blocks, x, height + 1, z);
            }
            break;
    }
}

// Générer un arbre selon le type spécifié
function generateTree(blocks, x, y, z, treeType = 'OAK') {
    if (x < 2 || x > 13 || z < 2 || z > 13 || y + 8 >= 128) return;
    
    let woodType, leavesType, trunkHeight, leavesLayers;
    
    switch (treeType) {
        case 'OAK':
            woodType = BLOCK_TYPES.OAK_WOOD;
            leavesType = BLOCK_TYPES.OAK_LEAVES;
            trunkHeight = 4 + Math.floor(Math.random() * 2);
            leavesLayers = 3;
            break;
        case 'BIRCH':
            woodType = BLOCK_TYPES.BIRCH_WOOD;
            leavesType = BLOCK_TYPES.BIRCH_LEAVES;
            trunkHeight = 5 + Math.floor(Math.random() * 2);
            leavesLayers = 2;
            break;
        case 'PINE':
            woodType = BLOCK_TYPES.PINE_WOOD;
            leavesType = BLOCK_TYPES.PINE_LEAVES;
            trunkHeight = 6 + Math.floor(Math.random() * 3);
            leavesLayers = 4;
            break;
        default:
            woodType = BLOCK_TYPES.WOOD;
            leavesType = BLOCK_TYPES.LEAVES;
            trunkHeight = 4 + Math.floor(Math.random() * 2);
            leavesLayers = 3;
    }
    
    // Générer le tronc
    for (let i = 0; i < trunkHeight; i++) {
        blocks[getIndex(x, y + i, z)] = woodType;
    }
    
    // Générer les feuilles selon le type d'arbre
    if (treeType === 'PINE') {
        // Forme conique pour les pins
        for (let ly = 0; ly < leavesLayers; ly++) {
            const radius = Math.max(1, leavesLayers - ly);
            const layerY = y + trunkHeight - 2 + ly;
            
            for (let lx = -radius; lx <= radius; lx++) {
                for (let lz = -radius; lz <= radius; lz++) {
                    const distance = Math.sqrt(lx * lx + lz * lz);
                    if (distance <= radius && Math.random() < 0.8) {
                        const leafX = x + lx;
                        const leafZ = z + lz;
                        if (leafX >= 0 && leafX < 16 && leafZ >= 0 && leafZ < 16) {
                            if (!(lx === 0 && lz === 0)) {
                                blocks[getIndex(leafX, layerY, leafZ)] = leavesType;
                            }
                        }
                    }
                }
            }
        }
    } else {
        // Forme ronde pour chênes et bouleaux
        const leavesY = y + trunkHeight - 1;
        for (let ly = 0; ly < leavesLayers; ly++) {
            const radius = ly === 0 ? 2 : (ly === leavesLayers - 1 ? 1 : 2);
            for (let lx = -radius; lx <= radius; lx++) {
                for (let lz = -radius; lz <= radius; lz++) {
                    if (Math.abs(lx) === radius && Math.abs(lz) === radius && Math.random() < 0.5) continue;
                    
                    const leafX = x + lx;
                    const leafZ = z + lz;
                    if (leafX >= 0 && leafX < 16 && leafZ >= 0 && leafZ < 16) {
                        if (!(lx === 0 && lz === 0)) {
                            blocks[getIndex(leafX, leavesY + ly, leafZ)] = leavesType;
                        }
                    }
                }
            }
        }
    }
}

// Générer un cactus
function generateCactus(blocks, x, y, z) {
    if (x < 0 || x >= 16 || z < 0 || z >= 16 || y + 3 >= 128) return;
    
    const height = 2 + Math.floor(Math.random() * 2);
    for (let i = 0; i < height; i++) {
        blocks[getIndex(x, y + i, z)] = BLOCK_TYPES.CACTUS;
    }
}

self.onmessage = function(e) {
    const { chunkX, chunkZ, type = 'generate' } = e.data;
    const blocks = new Array(16 * 128 * 16).fill(0); 
    const worldGen = new WorldGenerator();
    
    // Génération du terrain avec biomes améliorés
    for (let x = 0; x < 16; x++) {
        for (let z = 0; z < 16; z++) {
            const worldX = chunkX * 16 + x;
            const worldZ = chunkZ * 16 + z;
            
            // Obtenir les informations du biome
            const biomeValue = worldGen.getBiomeValue(worldX, worldZ);
            const biome = worldGen.getBiome(biomeValue);
            const baseHeight = worldGen.getHeight(worldX, worldZ);
            const detailNoise = worldGen.getDetailNoise(worldX, worldZ);
            const height = Math.floor(baseHeight + detailNoise);
            
            // Limiter la hauteur pour les performances
            const maxHeight = Math.min(height + 15, 128);
            
            // Générer les couches de blocs selon le biome
            for (let y = 0; y < maxHeight; y++) {
                const worldY = y;
                
                // Vérifier d'abord les grottes (mais pas trop près de la surface)
                if (y < height - 5 && worldGen.shouldHaveCave(worldX, worldY, worldZ)) {
                    blocks[getIndex(x, y, z)] = BLOCK_TYPES.AIR;
                    continue;
                }
                
                // Génération de la bedrock au fond
                if (y <= 2) {
                    blocks[getIndex(x, y, z)] = BLOCK_TYPES.BEDROCK;
                    continue;
                }
                
                // Génération selon la profondeur et le biome
                if (y < height - 8) {
                    // Couche profonde: pierre avec minerais
                    const oreType = worldGen.getOreType(worldX, worldY, worldZ);
                    if (oreType) {
                        blocks[getIndex(x, y, z)] = BLOCK_TYPES[oreType];
                    } else {
                        blocks[getIndex(x, y, z)] = BLOCK_TYPES.STONE;
                    }
                }
                else if (y < height - 2) {
                    // Couche intermédiaire selon le biome
                    switch (biome.name) {
                        case 'Beach':
                            blocks[getIndex(x, y, z)] = BLOCK_TYPES.SAND;
                            break;
                        case 'Mountains':
                            blocks[getIndex(x, y, z)] = Math.random() < 0.3 ? BLOCK_TYPES.STONE : BLOCK_TYPES.DIRT;
                            break;
                        default:
                            blocks[getIndex(x, y, z)] = BLOCK_TYPES.DIRT;
                    }
                }
                else if (y <= height) {
                    // Surface selon le biome
                    switch (biome.name) {
                        case 'Ocean':
                        case 'Beach':
                            blocks[getIndex(x, y, z)] = BLOCK_TYPES.SAND;
                            break;
                        case 'Mountains':
                            if (height > 90) {
                                blocks[getIndex(x, y, z)] = BLOCK_TYPES.SNOW;
                            } else if (height > 80) {
                                blocks[getIndex(x, y, z)] = BLOCK_TYPES.STONE;
                            } else {
                                blocks[getIndex(x, y, z)] = BLOCK_TYPES.GRASS;
                            }
                            break;
                        default:
                            blocks[getIndex(x, y, z)] = BLOCK_TYPES.GRASS;
                    }
                }
                
                // Génération d'eau seulement dans les zones appropriées
                const waterLevel = worldGen.waterLevel;
                if (y <= waterLevel && y > height && biome.name === 'Ocean') {
                    blocks[getIndex(x, y, z)] = BLOCK_TYPES.WATER;
                }
            }
            
            // Génération de végétation selon le biome
            generateVegetation(blocks, x, z, height, biome, biomeValue, worldGen);
        }
    }
    
    // Envoyer les données générées au thread principal
    self.postMessage({
        type: 'chunkGenerated',
        chunkX,
        chunkZ,
        blocks
    });
}; 